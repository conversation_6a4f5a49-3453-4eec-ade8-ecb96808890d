package com.bt.itsinner.domain.entity;

import java.util.List;

public class CmsProgram {
	private List<String> idList;	//用于批量读取or删除
	private String id;				//主键
	
	private Integer fontSize;		//字号
	private String fontType;		//字体
	private String fontColor;		//字体颜色
	private String backgroundColor;	//背景颜色
	private Integer holdTime;		//停留时间
	private Integer changeSpeed;	//变换速度
	private Integer inputMode;		//入屏方式
	private Integer outputMode;		//出屏方式
	private String imageInfo;		//位图信息
	private String txtInfo;			//原始文本信息---带换行符
	private String messageInfo;		//节目单信息---按深圳电明厂家协议打包的节目单信息
	
	private Integer cmsType;		//屏幕类型
	private Integer cmsWidth;		//屏幕宽度
	private Integer cmsHeight;		//屏幕高度
	
	private Integer index;			//排序号
	
	private String typeName;		//屏幕类型对应的名称
	
	public List<String> getIdList() {
		return idList;
	}
	public void setIdList(List<String> idList) {
		this.idList = idList;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public Integer getFontSize() {
		return fontSize;
	}
	public void setFontSize(Integer fontSize) {
		this.fontSize = fontSize;
	}
	public String getFontType() {
		return fontType;
	}
	public void setFontType(String fontType) {
		this.fontType = fontType;
	}
	public String getFontColor() {
		return fontColor;
	}
	public void setFontColor(String fontColor) {
		this.fontColor = fontColor;
	}
	public String getBackgroundColor() {
		return backgroundColor;
	}
	public void setBackgroundColor(String backgroundColor) {
		this.backgroundColor = backgroundColor;
	}
	public Integer getHoldTime() {
		return holdTime;
	}
	public void setHoldTime(Integer holdTime) {
		this.holdTime = holdTime;
	}
	public Integer getChangeSpeed() {
		return changeSpeed;
	}
	public void setChangeSpeed(Integer changeSpeed) {
		this.changeSpeed = changeSpeed;
	}
	public Integer getInputMode() {
		return inputMode;
	}
	public void setInputMode(Integer inputMode) {
		this.inputMode = inputMode;
	}
	public Integer getOutputMode() {
		return outputMode;
	}
	public void setOutputMode(Integer outputMode) {
		this.outputMode = outputMode;
	}
	public String getImageInfo() {
		return imageInfo;
	}
	public void setImageInfo(String imageInfo) {
		this.imageInfo = imageInfo;
	}
	public String getTxtInfo() {
		return txtInfo;
	}
	public void setTxtInfo(String txtInfo) {
		this.txtInfo = txtInfo;
	}
	public String getMessageInfo() {
		return messageInfo;
	}
	public void setMessageInfo(String messageInfo) {
		this.messageInfo = messageInfo;
	}
	public Integer getCmsType() {
		return cmsType;
	}
	public void setCmsType(Integer cmsType) {
		this.cmsType = cmsType;
	}
	public Integer getCmsWidth() {
		return cmsWidth;
	}
	public void setCmsWidth(Integer cmsWidth) {
		this.cmsWidth = cmsWidth;
	}
	public Integer getCmsHeight() {
		return cmsHeight;
	}
	public void setCmsHeight(Integer cmsHeight) {
		this.cmsHeight = cmsHeight;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public Integer getIndex() {
		return index;
	}
	public void setIndex(Integer index) {
		this.index = index;
	}

}
