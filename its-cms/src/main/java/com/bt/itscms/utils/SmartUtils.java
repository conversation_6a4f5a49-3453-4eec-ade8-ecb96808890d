package com.bt.itscms.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;

import com.bt.itscms.domain.dto.CmsStyle;
import com.bt.itscms.domain.dto.CmsStyleConsts;
import com.bt.itscms.domain.dto.DataConstants;
import com.bt.itscms.domain.dto.PublishDTO;
import com.bt.itscms.domain.dto.SysConsts;
import com.bt.itscms.domain.vo.CmsRepertoryVO;
import com.bt.itscms.domain.vo.DeviceCmsVO;
import com.google.gson.Gson;

/**
 * Smart工具
 */
public class SmartUtils {
    /**
     * 换行符
     */
    private static final String FLAG = "</br>";

    /**
     * 自定义分隔符，避免与常用的符号区别
     */
    public static final String FLAG_SPLIT = "☺";

    /**
     * 赤道半径
     */
    private static double EARTH_RADIUS = 6378.137;

    /**
     * 模式字符串
     */
    private static String REGEX = "[\\x00-\\xff]+";

    /**
     * 智能计算与分屏
     *
     * @param specs
     * @param message
     */
    public synchronized static List<CmsRepertoryVO> smartAutoSplitview(String specs, String message) {
        Gson gson = new Gson();
        List<CmsRepertoryVO> repertories = new ArrayList<CmsRepertoryVO>();
        if (specs != null && StringUtils.isNotBlank(specs)) {
            message = message.replace(" ", "");//去除空格
            String[] specsStrArr = specs.replace("*", "-").split("-");
            int width = Integer.valueOf(specsStrArr[0].trim());
            int height = Integer.valueOf(specsStrArr[1].trim());
            // 适配字体
            Map<String, Integer> firstMap = autoAdapterFont(width, height, message);
            if (firstMap.get("fitFont") != null) {
                String enterResult = smartInfoEnterWithREGEX(message, firstMap.get("maxColumn"));
                CmsStyle style = autoAdapterStyle(width, height, firstMap.get("font"), enterResult);
                repertories.add(gson.fromJson(gson.toJson(style), CmsRepertoryVO.class));
            } else {
                // 需要分屏
                String[] viewInfo = smartInfoEnterWithREGEX(message, firstMap.get("maxShow")).split(FLAG);
                for (String info : viewInfo) {
                    Map<String, Integer> otherMap = autoAdapterFont(width, height, info);
                    if (otherMap.get("fitFont") == null) {
                        otherMap.put("fitFont", otherMap.get("font"));
                    }
                    String enterResult = smartInfoEnterWithREGEX(info, otherMap.get("maxColumn"));
                    CmsStyle style = autoAdapterStyle(width, height, otherMap.get("fitFont"), enterResult);
                    repertories.add(gson.fromJson(gson.toJson(style), CmsRepertoryVO.class));
                }
            }
        }
        return repertories;
    }

    /**
     * 自动适配字体
     *
     * @param width
     * @param height
     * @param message
     * @return
     */
    private static Map<String, Integer> autoAdapterFont(int width, int height, String message) {
        boolean choose = true;
        int aptIndex = 0;
        int font = 0;
        int maxColumn = 0;
        int maxRow = 0;
        int maxShow = 0;
        int fitFont = 0;
        List<Integer> fonts = Arrays.asList(CmsStyleConsts.FONT4, CmsStyleConsts.FONT3, CmsStyleConsts.FONT2, CmsStyleConsts.FONT1);
        while (choose && aptIndex < fonts.size()) {
            if (DataConstants.SMART_SPLITVIEW_MODE == SysConsts.SAPLITVIEW_MODE_LIUZHOU) {
                // 寻找最佳字体（柳州对字体有要求，需要匹配一下字体库）
                if (LiuzhouFontStoreUtils.existLiuzhouFont(width + "*" + height, fonts.get(aptIndex))) {
                    font = fonts.get(aptIndex);
                    maxColumn = width / font;
                    maxRow = height / font;
                    maxShow = maxColumn * maxRow;
                    if (message.length() <= maxShow) {
                        // 停止选择
                        fitFont = font;
                        choose = false;
                    }
                }
            }else {
                font = fonts.get(aptIndex);
                maxColumn = width / font;
                maxRow = height / font;
                maxShow = maxColumn * maxRow;
                if (message.length() <= maxShow) {
                    // 停止选择
                    fitFont = font;
                    choose = false;
                }
            }
            ++aptIndex;
        }
        // 判断选择结束的条件
        Map<String, Integer> param = new HashMap<String, Integer>();
        param.put("maxColumn", maxColumn);
        param.put("maxRow", maxRow);
        param.put("maxShow", maxShow);
        param.put("font", font);
        if (!choose) {
            param.put("fitFont", fitFont);//找到最佳字体
        }
        return param;
    }

    /**
     * 自动适配样式
     *font = fonts.get(aptIndex);
     * @param width
     * @param height
     * @param font
     * @param enterResult
     * @return
     */
    private static CmsStyle autoAdapterStyle(int width, int height, int font, String enterResult) {
        CmsStyle style = new CmsStyle();
        String[] split = enterResult.split(FLAG);
        Optional<String> optional = Arrays.stream(split)
                .collect(Collectors.maxBy((e1, e2) -> Integer.compare(e1.length(), e2.length())));
        /*int label_x = Math.round((width - font * optional.get().length()) / 2.0f);
        int label_y = Math.round((height - font * split.length) / 2.0f);*/
        Matcher matcher = Pattern.compile(REGEX).matcher(optional.get());
        int posi=0;
        while (matcher.find()){
            String s = String.valueOf(Math.floor(matcher.group().length() / 2.0d));
            posi += Integer.valueOf(s.substring(0,s.indexOf(".")));
        }
        int label_x = (width - font * (optional.get().length()-posi)) / 2;
        int label_y = (height - font * split.length) / 2;
        style.setLabelX(label_x > 0 ? label_x : 0);
        style.setLabelY(label_y > 0 ? label_y : 0);
        // 设置字体
        style.setFontSize(font);
        // 设置消息
        style.setMessageBody(enterResult);
        // 设置对齐方式(居中)
        style.setLayout(CmsStyleConsts.CENTER_MARK);
        return style;
    }

    /**
     * 智能消息分屏/换行
     *
     * @param massege
     * @param offset
     * @return
     */
    private static String smartInfoEnter(String massege, int offset) {
        // 系统自动分配
        StringBuffer builder = new StringBuffer(massege);
        if (builder.length() > offset) {
            int index = builder.length() / offset;
            for (int i = 1; i <= index; i++) {
                int l = offset + (i - 1) * (offset + FLAG.length());
                builder.insert(l, FLAG);
            }
        }
        // 消息是偏移值的整数倍时，需要去掉尾部多余的flag标志
        if (massege.length() % offset == 0 && builder.indexOf(FLAG) != -1) {
            builder.delete(builder.lastIndexOf(FLAG), builder.length());
        }
        return builder.toString();
    }

    /**
     * 带模式匹配的智能分屏/换行
     *
     * @param massege
     * @param offset
     * @return
     */
    private static String smartInfoEnterWithREGEX(String massege, int offset) {
        StringBuilder msg = new StringBuilder(massege);
        if (msg.length() <= offset) {
            return msg.toString();
        }
        int ist = 0;
        int flag = -1;
        while (ist < msg.length()) {
            ist += offset;
            if (ist < msg.length()) {
                Pattern compile = Pattern.compile(REGEX);
                Matcher matcher = compile.matcher(msg);
                int posi = 0;
                while (matcher.find()) {
                    if (matcher.start() > flag && matcher.start() <= ist) {
                        String s = String.valueOf(Math.floor(matcher.group().length() / 2.0d));
                        posi += Integer.valueOf(s.substring(0,s.indexOf(".")));
                       /* posi += Math.round(matcher.group().length()/2.0d);*/
                        flag = matcher.start();
                    }
                }
                ist += posi;
                msg.insert(ist, FLAG_SPLIT);
            }
            ist += FLAG_SPLIT.length();
        }
        return msg.toString().replace(FLAG_SPLIT, FLAG);
    }

    /**
     * 通过经纬度获取距离(单位：m)
     *
     * @param originLng
     * @param originLat
     * @param destinationLng
     * @param destinationLat
     * @return
     */
    public static long reckonDistanceByLngAndLat(String originLng, String originLat, String destinationLng, String destinationLat) {
        if (originLng != null && originLat != null && destinationLng != null && destinationLat != null) {
            // 纬度
            double lat1 = Math.toRadians(Double.valueOf(originLat));
            double lat2 = Math.toRadians(Double.valueOf(destinationLat));
            // 经度
            double lng1 = Math.toRadians(Double.valueOf(originLng));
            double lng2 = Math.toRadians(Double.valueOf(destinationLng));
            // 纬度之差
            double a = lat1 - lat2;
            // 经度之差
            double b = lng1 - lng2;
            // 计算两点距离的公式
            double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
                    Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(b / 2), 2)));
            // 弧长乘地球半径, 返回单位: 千米
            s = s * EARTH_RADIUS;
            // 返回结果，单位：米
            return Math.round(s) * 1000;
        }
        return 0;
    }

    /**
     * 根据桩号计算公路距离(单位:米)
     *
     * @param startPost 情报板桩号
     * @param endPost   //事件点桩号
     * @return
     */
    public synchronized static int reckonDistanceByMilePost(String startPost, String endPost) {
        if (StringUtils.isNotBlank(startPost) && StringUtils.isNotBlank(endPost)) {
            // 起始桩号
            StringBuilder startBuilder = new StringBuilder();
            startBuilder.append(startPost.toUpperCase().replace("+", "-"));
            if (startBuilder.indexOf("-") == -1) {
                // 说明桩号只有前3位
                startBuilder.append("-0");
            }
            if (startBuilder.indexOf("K") != -1) {
                startBuilder.deleteCharAt(startBuilder.indexOf("K"));
            }
            // 截止桩号
            StringBuilder endBuilder = new StringBuilder();
            endBuilder.append(endPost.toUpperCase().replace("+", "-"));
            if (endBuilder.indexOf("-") == -1) {
                // 说明桩号只有前3位
                endBuilder.append("-0");
            }
            if (endBuilder.indexOf("K") != -1) {
                endBuilder.deleteCharAt(endBuilder.indexOf("K"));
            }
            // 提取数值
            List<String> startList = Arrays.asList(startBuilder.toString().split("-"));
            List<String> endList = Arrays.asList(endBuilder.toString().split("-"));
            // 计算距离
            int start = 0;
            int end = 0;
            if (StringUtils.isNotBlank(startList.get(0))) {
                start = Integer.valueOf(startList.get(0).trim()) * 1000;
            }
            if (StringUtils.isNotBlank(endList.get(0))) {
                end = Integer.valueOf(endList.get(0).trim()) * 1000;
            }
            int startDistance = start + Integer.valueOf(startList.get(1).trim());
            int endDistance = end + Integer.valueOf(endList.get(1).trim());
            return startDistance - endDistance;
        }
        return 0;
    }

    /**
     * 情报板诱导规则
     *
     * @param cmsList
     * @param publishVo
     * @return
     */
    public static List<DeviceCmsVO> filterDeviceCms(List<DeviceCmsVO> cmsList, PublishDTO publishVo) {
        List<DeviceCmsVO> result = new ArrayList<DeviceCmsVO>();
        if (DataUtils.isEmptyData(cmsList)) {
            return result;
        }
        cmsList = cmsList.stream().filter(e -> !e.getSpecs().equals("48*48")).collect(Collectors.toList()); // 过滤48*48规格
        if (publishVo != null && publishVo.getParentRoad() != null && publishVo.getMilePost() != null) { // 事件需要进行相应规则处理
            // 按照路段分组
            Map<String, List<DeviceCmsVO>> roadParentMap = cmsList.stream().collect(Collectors.groupingBy(DeviceCmsVO::getParentRoad));
            for (String roadKey : roadParentMap.keySet()) {
                if (roadKey.equals(publishVo.getParentRoad())) {
                    // 同路段，区分上下行数据
                    List<DeviceCmsVO> deviceCmsVos = roadParentMap.get(roadKey).stream().filter(e -> {
                        if (DataUtils.isNotNull(e.getDirectionInfo()) && "上行".equals(e.getDirectionInfo())) {
                            return SmartUtils.reckonDistanceByMilePost(e.getMilePost(), publishVo.getMilePost()) < 0;
                        }
                        if (DataUtils.isNotNull(e.getDirectionInfo()) && "下行".equals(e.getDirectionInfo())) {
                            return SmartUtils.reckonDistanceByMilePost(e.getMilePost(), publishVo.getMilePost()) > 0;
                        }
                        return false;
                    }).collect(Collectors.toList());
                    if (!DataUtils.isEmptyData(deviceCmsVos)) {
                        result.addAll(deviceCmsVos);
                    }
                } else {
                    // 不同路段
                    result.addAll(roadParentMap.get(roadKey));
                }
            }
        } else {
            // 告警不做处理
            result.addAll(cmsList);
        }
        return result;
    }

    /*public static void main(String[] args) {
        String msg = "梧州往柳州方向距象州北收费站约1km发生交通事故，过往车辆请小心慢行";
        *//* System.out.println(smartInfoEnterWithREGEX(msg,8));*//*
        Pattern compile = Pattern.compile(REGEX);
        Matcher matcher = compile.matcher(msg);
        while (matcher.find()){
            System.out.println(matcher.start());
            System.out.println(matcher.group());
            System.out.println(matcher.group().length());
        }
    }*/

}
