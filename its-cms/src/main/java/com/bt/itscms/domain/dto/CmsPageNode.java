package com.bt.itscms.domain.dto;

import java.util.List;

import com.bt.itscms.domain.vo.CmsRepertoryVO;

public class CmsPageNode {
	private String id;//pageId
	private String text;
	private List<CmsRepertoryVO> children;//必须用“children”标签才能识别为子节点
	private String accountor;
	private Integer appended = 0;//是否是追加发布 0-否 1-是
	private Integer cmsType;
	private Integer width;
	private Integer height;
	private String deviceId;//设备单发用
	private List<String> idList;//设备群发用
	private String writeDate;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public List<CmsRepertoryVO> getChildren() {
		return children;
	}

	public void setChildren(List<CmsRepertoryVO> children) {
		this.children = children;
	}

	public String getAccountor() {
		return accountor;
	}

	public void setAccountor(String accountor) {
		this.accountor = accountor;
	}


	public Integer getAppended() {
		return appended;
	}

	public void setAppended(Integer appended) {
		this.appended = appended;
	}

	public Integer getCmsType() {
		return cmsType;
	}

	public void setCmsType(Integer cmsType) {
		this.cmsType = cmsType;
	}

	public Integer getWidth() {
		return width;
	}

	public void setWidth(Integer width) {
		this.width = width;
	}

	public Integer getHeight() {
		return height;
	}

	public void setHeight(Integer height) {
		this.height = height;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public List<String> getIdList() {
		return idList;
	}

	public void setIdList(List<String> idList) {
		this.idList = idList;
	}

	public String getWriteDate() {
		return writeDate;
	}

	public void setWriteDate(String writeDate) {
		this.writeDate = writeDate;
	}
}
