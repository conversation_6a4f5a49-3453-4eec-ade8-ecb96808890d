package com.bt.itsuser.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bt.itscore.domain.dto.MessageDTO;

@FeignClient(name = "its-websocket")
public interface ItsWebSocketFeignClient {

	@GetMapping(value="/message/pushSomeUser")
	/** 发送websocket消息给某些用户  **/
	public void pushSomeUser(@RequestBody MessageDTO messageDTO);

}
