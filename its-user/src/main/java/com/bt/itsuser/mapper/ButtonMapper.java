package com.bt.itsuser.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.bt.itscore.domain.dto.IdIntegerBatchDTO;
import com.bt.itsuser.domain.dto.ButtonDTO;
import com.bt.itsuser.domain.dto.RoleButtonDTO;
import com.bt.itsuser.domain.vo.ButtonVO;

@Mapper
public interface ButtonMapper {

	int add(ButtonDTO buttonDTO);

	ButtonVO selectByPermissionsId(Map<String, Object> map);

	List<ButtonVO> selectAll(ButtonDTO buttonDTO);

	int statByButtonNamePermissionsId(Map<String, Object> map);

	int update(ButtonDTO buttonDTO);

	int delete(IdIntegerBatchDTO idIntegerBatchDTO);

	List<ButtonVO> selectByMenuId(ButtonDTO buttonDTO);

	List<ButtonVO> selectByRoleId(RoleButtonDTO roleButtonDTO);

}
