package com.bt.itsuser.config;
import java.util.*;
import java.util.stream.Collectors;

import com.bt.itscore.domain.dto.RoleAlarmDetailDTO;
import com.bt.itscore.domain.vo.DictItemVO;
import com.bt.itsuser.service.DictService;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.bt.itscore.domain.vo.UserCccVO;
import com.bt.itscore.utils.AESUtils;
import com.bt.itscore.utils.AliyunSmsUtils;
import com.bt.itscore.utils.DESUtils;
import com.bt.itsuser.domain.vo.ButtonVO;
import com.bt.itsuser.domain.vo.FacilityVO;
import com.bt.itsuser.domain.vo.RoadVO;
import com.bt.itsuser.domain.vo.RoleAlarmDetailVO;
import com.bt.itsuser.domain.vo.RoleFacilityDeviceVO;
import com.bt.itsuser.domain.vo.RoleFacilityVO;
import com.bt.itsuser.domain.vo.RoleRoadVO;
import com.bt.itsuser.domain.vo.RoleVO;
import com.bt.itsuser.domain.vo.SysGlobalVarVO;
import com.bt.itsuser.domain.vo.UserGzhVO;
import com.bt.itsuser.service.DataPermissionsService;
import com.bt.itsuser.service.RoleService;
import com.bt.itsuser.service.UserService;

@Component
//@Order(1) //指定顺序
public class InitConfig implements CommandLineRunner {

	private final static Logger LOGGER = LoggerFactory.getLogger(InitConfig.class);
	private final static String CACHE_ALARM_TYPE_KEY = "AlarmConfig-alarmTypeData";
    @Value("${server.cipher.key}")
    private String serverCipherKey;

    @Value("${server.cipher.iv}")
    private String serverCipherIv;

    @Value("${server.aes.key}")
    private String serverAesKey;

    @Value("${server.aes.iv}")
    private String serverAesIv;
    @Value("${sms.use.other}")
    private int smsUseOther;
	@Value("${sms.access.key}")
    private String smsAccessKey;
	@Value("${sms.access.secret}")
    private String smsAccessSecret;
	@Value("${sms.sign.name}")
    private String smsSignName;
	@SuppressWarnings("rawtypes")
	@Autowired
	private RedisTemplate redisTemplate;
	@Autowired
	private StringRedisTemplate stringRedisTemplate;
	@Autowired
	private UserService userService;
    @Autowired
	private RoleService roleService;
	@Autowired
	DataPermissionsService dataPermissionsService;

	@Autowired
	private DictService dictService;

	@Override
	public void run(String... args) throws Exception {
		LOGGER.info("InitConfig 初始化资源===========");

		DESUtils.initCipher(serverCipherKey, serverCipherIv);
        AESUtils.initCipher(serverAesKey, serverAesIv);
        if (smsUseOther == 1) {
			LOGGER.info("启用sms.yaml的配置sms.use.other：{}", smsUseOther);
			AliyunSmsUtils.init(smsAccessKey, smsAccessSecret, smsSignName);
		}

		cacheRoleButton();

		cacheRoleFacility();

		cacheRoleRoad();

		cacheRoleFacilityDeviceDir();

		cacheUserCccButton();

		cacheSysGlobalVar();

    	cacheUserGzh();

    	cacheRoleAlarm();

    	LOGGER.info("InitConfig 初始化资源完成===========");
	}

	/**
	 * @描述 缓存角色按钮权限
	 */
	private void cacheRoleButton() {
		//查询role_id-buttons
		List<RoleVO> list = roleService.selectAllWithButton();
		for (RoleVO roleVO : list) {
			List<ButtonVO> buttons = roleVO.getButtons();
			String all = ";";
			for (ButtonVO btnVO : buttons) {
				all += btnVO.getPermissionsId() + ";";
			}
			redisTemplate.opsForValue().set(roleVO.getId(), all);
		}
	}
	
	/**
	 * @描述 缓存角色设施权限
	 */
	private void cacheRoleFacility() {
//	    redisTemplate.opsForValue().set("00e59f7a06244761a7ffccfe6f99b859", "00e59f7a06244761a7ffccfe6f99b859");
		List<RoleFacilityVO> roleFacilitys = dataPermissionsService.selectRoleFacility();
		if(!CollectionUtils.isEmpty(roleFacilitys)) {
			for (RoleFacilityVO vo : roleFacilitys) {
				String facility = ";";
				if(vo.getFacilitys() != null) {
					for (FacilityVO f : vo.getFacilitys()) {
						facility += f.getFacilityNo() + ";";
					}
					LOGGER.info("{}:facility:{}", vo.getRoleId(), facility);
					redisTemplate.opsForValue().set(vo.getRoleId()+"-facility", facility);
				}
			}
		}
	}
	
	/**
	 * @描述 缓存角色路段权限
	 */
	private void cacheRoleRoad() {
		List<RoleRoadVO> roleRoads = dataPermissionsService.selectRoleRoad();
		if(!CollectionUtils.isEmpty(roleRoads)) {
			for (RoleRoadVO vo : roleRoads) {
				String road = ";";
				if(vo.getRoads() != null) {
					for (RoadVO r : vo.getRoads()) {
						road += r.getRoadNo() + ";";
					}
					LOGGER.info("{}:road:{}", vo.getRoleId(), road);
					redisTemplate.opsForValue().set(vo.getRoleId()+"-road", road);
				}
			}
		}
	}
	
	/**
	 * @描述 缓存角色设施设备目录权限（首页地图上的设施设备等目录权限）
	 */
	private void cacheRoleFacilityDeviceDir() {
		List<RoleFacilityDeviceVO> roleFacilityDeviceDirs = dataPermissionsService.selectRoleFacilityDeviceDir();
		if(!CollectionUtils.isEmpty(roleFacilityDeviceDirs)) {
			for (RoleFacilityDeviceVO vo : roleFacilityDeviceDirs) {
				String facilityDeviceDirs = ";";
				List<Integer> facilityDeviceDirIds = vo.getFacilityDeviceDirIds();
				if(facilityDeviceDirIds != null) {
					for (Integer d : facilityDeviceDirIds) {
						facilityDeviceDirs += d + ";";
					}
					redisTemplate.opsForValue().set(vo.getRoleId()+"-facilityDeviceDir", facilityDeviceDirs);
				}
			}
		}
	}
	
	/**
	 * @描述 缓存阿里云云呼叫中心坐席按钮权限
	 */
	private void cacheUserCccButton() {
		//用户-全局按钮绑定，目前只有云呼叫中心坐席绑定用户，后续增加需改写从user_button表获取，云呼坐席创建时更新user_button表
    	//查询用户、云呼叫中心坐席绑定（后续按需要优化）
    	List<UserCccVO> userCccs = dataPermissionsService.selectUserCcc();
    	for(UserCccVO vo : userCccs) {
			if(StringUtils.isNotBlank(vo.getUserId())) {
				String userIds = vo.getUserId();
				LOGGER.info("userCcc:{}", userIds);
				redisTemplate.opsForValue().set(userIds + "-button", "global:tel:call");
			}
    	}
	}

	/**
	 * @描述 缓存系统全局变量表数据sys_global_var
	 */
	private void cacheSysGlobalVar() {
		// 加载全局变量sys_global_var，并载入到redis
    	List<SysGlobalVarVO> sysGlobalVars = userService.selectSysGlobalVar();
    	if(!CollectionUtils.isEmpty(sysGlobalVars)) {
    		for (SysGlobalVarVO sysGlobalVarVO : sysGlobalVars) {
    			redisTemplate.opsForValue().set(sysGlobalVarVO.getVarName(), sysGlobalVarVO.getVarValue());
			}
    	}
	}
	
	/**
	 * @描述 缓存公众号上已注册的用户信息（openid、手机号）
	 */
	private void cacheUserGzh() {
		List<UserGzhVO> selectAllUserGzh = userService.selectAllUserGzh();
		if(!CollectionUtils.isEmpty(selectAllUserGzh)) {
			for (UserGzhVO userGzhVO : selectAllUserGzh) {
				String account = userGzhVO.getAccount();
				String openid = userGzhVO.getOpenid();
				if(StringUtils.isNotBlank(account) && StringUtils.isNotBlank(openid)) {
					stringRedisTemplate.opsForValue().set("user:openid-" + openid, account);
				}
			}
		}
	}

	/**
	 * @描述 缓存角色告警权限(是否推送告警消息)
	 */
	private void cacheRoleAlarm() {
		//查询role_alarm
		List<RoleVO> list = roleService.selectAllWithAlarm();
		for (RoleVO roleVO : list) {
			stringRedisTemplate.opsForValue().set("its-user:role_alarm_" + roleVO.getId(), "1");
		}

		//查询role_alarm_detail
		List<RoleAlarmDetailVO> allAlarmDetail = roleService.selectAllAlarmDetail();
		allAlarmDetail.stream()
				.collect(Collectors.groupingBy(RoleAlarmDetailVO::getRoleId, LinkedHashMap::new, Collectors.toList()))
				.forEach((roleId, alarmDetailList) -> {
					List<RoleAlarmDetailDTO> tmp = new ArrayList<>();
					for (RoleAlarmDetailVO alarmDetail : alarmDetailList) {
						RoleAlarmDetailDTO item = new RoleAlarmDetailDTO();
						BeanUtils.copyProperties(alarmDetail, item);
						tmp.add(item);
					}
					redisTemplate.opsForValue().set("its-user:role_alarm_type_" + roleId, tmp);
				});

		LOGGER.info("角色告警权限数据缓存完成");
	}

}
