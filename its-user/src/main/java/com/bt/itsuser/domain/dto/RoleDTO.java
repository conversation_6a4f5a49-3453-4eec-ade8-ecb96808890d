package com.bt.itsuser.domain.dto;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class RoleDTO {

	private String id;
	@NotBlank(message = "角色名称不能为空")
	private String roleName;
	@Min(value = 1, message = "sort必须是大于1的整数")
	@Min(value = 1, message = "sort不能小于1")
	@NotNull(message = "sort不能为空")
	private Integer sort;//排序号
	@Min(value = 0, message = "use值只能为0或1")
	@Max(value = 1, message = "use值只能为0或1")
	@NotNull(message = "use不能为空")
	private Integer use;//启用状态1启用0未启用
	private String orgId;//所属公司

	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getRoleName() {
		return roleName;
	}
	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public Integer getUse() {
		return use;
	}
	public void setUse(Integer use) {
		this.use = use;
	}
	public String getOrgId() {
		return orgId;
	}
	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
}
