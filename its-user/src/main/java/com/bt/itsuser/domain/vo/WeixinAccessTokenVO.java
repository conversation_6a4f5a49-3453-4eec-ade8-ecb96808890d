package com.bt.itsuser.domain.vo;
/**
* <AUTHOR>
* @date 2022年1月19日 下午6:49:19
* @Description 微信公众号获取access_token使用
 */
public class WeixinAccessTokenVO {
	private Integer code;
	private String access_token;
//	private Integer expires_in;
	private String refresh_token;
	private String openid;
	private String unionid;
//	private String scope;
	public String getAccess_token() {
		return access_token;
	}
	public Integer getCode() {
		return code;
	}
	public void setCode(Integer code) {
		this.code = code;
	}
	public void setAccess_token(String access_token) {
		this.access_token = access_token;
	}
	public String getRefresh_token() {
		return refresh_token;
	}
	public void setRefresh_token(String refresh_token) {
		this.refresh_token = refresh_token;
	}
	public String getOpenid() {
		return openid;
	}
	public void setOpenid(String openid) {
		this.openid = openid;
	}
	public String getUnionid() {
		return unionid;
	}
	public void setUnionid(String unionid) {
		this.unionid = unionid;
	}
}
