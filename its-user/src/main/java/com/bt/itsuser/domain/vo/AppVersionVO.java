package com.bt.itsuser.domain.vo;

public class AppVersionVO {
	private Integer id;// int(11) NOT NULL AUTO_INCREMENT,
	private Integer appType;// int(11) NOT NULL COMMENT '1-安卓 2-IOS',
	private String versionNo;// varchar(20) NOT NULL COMMENT '系统版本号',
	private Long createTime;// bigint(20) NOT NULL COMMENT '该版本发布时间',
	private Integer lastestVersion;// int(11) NOT NULL DEFAULT '1' COMMENT '0-不是最新版本 1-最新版本',
	private String apkName;// varchar(20) DEFAULT NULL COMMENT 'apk下载名称',
	private String fileUrl;// varchar(100) DEFAULT NULL COMMENT '下载路径',
	private String appDesc;// varchar(500) DEFAULT NULL COMMENT '版本描述',
	private Integer force;// int(11) DEFAULT '1' COMMENT '强制更新 1-强制  0不强制',

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getAppType() {
		return appType;
	}

	public void setAppType(Integer appType) {
		this.appType = appType;
	}

	public String getVersionNo() {
		return versionNo;
	}

	public void setVersionNo(String versionNo) {
		this.versionNo = versionNo;
	}

	public Long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public Integer getLastestVersion() {
		return lastestVersion;
	}

	public void setLastestVersion(Integer lastestVersion) {
		this.lastestVersion = lastestVersion;
	}

	public String getApkName() {
		return apkName;
	}

	public void setApkName(String apkName) {
		this.apkName = apkName;
	}

	public String getFileUrl() {
		return fileUrl;
	}

	public void setFileUrl(String fileUrl) {
		this.fileUrl = fileUrl;
	}

	public String getAppDesc() {
		return appDesc;
	}

	public void setAppDesc(String appDesc) {
		this.appDesc = appDesc;
	}

	public Integer getForce() {
		return force;
	}

	public void setForce(Integer force) {
		this.force = force;
	}
}
