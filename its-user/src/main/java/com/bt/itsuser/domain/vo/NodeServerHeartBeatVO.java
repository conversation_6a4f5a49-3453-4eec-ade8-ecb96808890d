package com.bt.itsuser.domain.vo;

public class NodeServerHeartBeatVO {
	private Integer id;
	private String serviceName;
	private Integer sourceId;
	private String sourceName;
	private String createTime;
	private String websocketType = "NodeServerHeartBeat";
	private Long checkTime;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getServiceName() {
		return serviceName;
	}

	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}

	public Integer getSourceId() {
		return sourceId;
	}

	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}

	public String getSourceName() {
		return sourceName;
	}

	public void setSourceName(String sourceName) {
		this.sourceName = sourceName;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getWebsocketType() {
		return websocketType;
	}

	public void setWebsocketType(String websocketType) {
		this.websocketType = websocketType;
	}

	public Long getCheckTime() {
		return checkTime;
	}

	public void setCheckTime(Long checkTime) {
		this.checkTime = checkTime;
	}
}
