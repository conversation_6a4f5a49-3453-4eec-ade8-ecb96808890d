package com.bt.itsuser.domain.dto;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 
 * <AUTHOR>
 * @date 2021年3月11日 下午5:17:02
 * @Description 字典类型
 */
public class DictTypeDTO {
	private Integer id;
	@NotBlank(message = "字典类型名称不能为空")
	private String name;//字典类型名称
	private String remark;//备注
	@Min(value = 0, message = "use值只能为0或1")
	@Max(value = 1, message = "use值只能为0或1")
	@NotNull(message = "use不能为空")
	private Integer use;//启用状态1启用0未启用
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public Integer getUse() {
		return use;
	}
	public void setUse(Integer use) {
		this.use = use;
	}
}
