package com.bt.itsuser.domain.vo;
/**
* <AUTHOR>
* @date 2022年7月11日 下午3:35:46
* @Description 系统全局变量VO（对应数据库sys_global_var）
 */
public class SysGlobalVarVO {

	private Integer id;// 主键
	private String varName;// 变量名
	private String varValue;// 变量值
	private String remark;// 备注说明

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getVarName() {
		return varName;
	}

	public void setVarName(String varName) {
		this.varName = varName;
	}

	public String getVarValue() {
		return varValue;
	}

	public void setVarValue(String varValue) {
		this.varValue = varValue;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

}
