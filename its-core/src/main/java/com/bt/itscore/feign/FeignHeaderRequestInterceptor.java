package com.bt.itscore.feign;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import feign.RequestInterceptor;
import feign.RequestTemplate;

public class FeignHeaderRequestInterceptor implements RequestInterceptor {

	@Override
	public void apply(RequestTemplate template) {
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if(attributes != null) {
			HttpServletRequest request = attributes.getRequest();
			String token = request.getHeader("Authorization");
			String type = request.getHeader("AuthorizationType");
			template.header("Authorization", token);
			if(StringUtils.isNotBlank(type)) {
				template.header("AuthorizationType", type);
				template.header("ykSolt", request.getHeader("ykSolt"));
				template.header("openid", request.getHeader("openid"));
				template.header("accessToken", request.getHeader("accessToken"));
			}
		}
	}

}