package com.bt.itscore.domain.dto;

import java.util.List;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 *
 *
 * <AUTHOR>
 * @date 2021-03-19 17:02:01
 */
public class DeviceDTO {
    
    /**
     * 设备id-主键
     */
    private String deviceId;

	/**
	 * 设备ip
	 */
	private String deviceIp;
	/**
	 * 设施NO-facility表主键
	 */
	private String facilityNo;
	/**
	 * 方向NO-direction表主键
	 */
	@NotBlank(message="方向不能为空")
	private String directionNo;
	/**
	 * 拍摄方向
	 */
	private String shootDirectionNo;
	/**
	 * 设备名称
	 */
	@NotBlank(message = "名称不能为空")
	private String deviceName;
	
	/**
	 * 设备名称简称
	 */
	private String deviceShortName;
	private String deviceNameRemark;
	/**
	 * 设备类型 1车检器  2情报板  4 视频   6隧道类型  10门架
	 */
	@Min(value = 1)
	private Integer deviceTypeNo;
	/**
	 * 桩号
	 */
	private String milePost;
	/**
	 * 桩号对应的double值(单位:km)
	 */
	private Integer mpValue;
	/**
	 * 是否启用  (0停用，1在用)
	 */
	@Min(value = 0, message = "use值只能为0或1")
	@Max(value = 1, message = "use值只能为0或1")
	@NotNull(message = "use不能为空")
	private Integer use;
	private Integer delStatus;//1停用，0启用
	/**
	 * 状态(0离线 1在线)
	 */
	private Integer status;
	/**
	 * 经度
	 */
	private String lng;
	/**
	 * 纬度
	 */
	private String lat;
	/**
	 * 路段编号
	 */
	private Integer roadNo;
	/**
	 * 计算经纬度开关0关闭 1打开
	 */
	private Integer computeSwitch;
	
	private Integer facilityTypeNo;
	
	private List<Integer> ids; //批量删除
	
	private String tableName;   //子表名称
	private Integer sourceId;
	private Integer sort;//排序
	private String spUpdateTime;//共享平台更新时间
	private String roadName;//路段名称
	
	/**
	 * 设置：设备id-主键
	 */
	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}
	
	/**
	 * 获取：设备id-主键
	 */
	public String getDeviceId() {
		return deviceId;
	}
	
	public String getDeviceIp() {
        return deviceIp;
    }

    public void setDeviceIp(String deviceIp) {
        this.deviceIp = deviceIp;
    }

    /**
	 * 设置：设施NO-facility表主键
	 */
	public void setFacilityNo(String facilityNo) {
		this.facilityNo = facilityNo;
	}
	/**
	 * 获取：设施NO-facility表主键
	 */
	public String getFacilityNo() {
		return facilityNo;
	}
	/**
	 * 设置：方向NO-direction表主键
	 */
	public void setDirectionNo(String directionNo) {
		this.directionNo = directionNo;
	}
	/**
	 * 获取：方向NO-direction表主键
	 */
	public String getDirectionNo() {
		return directionNo;
	}
	public String getShootDirectionNo() {
		return shootDirectionNo;
	}
	
	public void setShootDirectionNo(String shootDirectionNo) {
		this.shootDirectionNo = shootDirectionNo;
	}
	
	/**
	 * 设置：设备名称
	 */
	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}
	/**
	 * 获取：设备名称
	 */
	public String getDeviceName() {
		return deviceName;
	}
	
	public String getDeviceShortName() {
		return deviceShortName;
	}
	
	public void setDeviceShortName(String deviceShortName) {
		this.deviceShortName = deviceShortName;
	}
	
	public String getDeviceNameRemark() {
		return deviceNameRemark;
	}
	
	public void setDeviceNameRemark(String deviceNameRemark) {
		this.deviceNameRemark = deviceNameRemark;
	}
	
	/**
	 * 设置：设备类型 1车检器  2情报板  4 视频  5隧道类型
	 */
	public void setDeviceTypeNo(Integer deviceTypeNo) {
		this.deviceTypeNo = deviceTypeNo;
	}
	/**
	 * 获取：设备类型 1车检器  2情报板  4 视频  5隧道类型
	 */
	public Integer getDeviceTypeNo() {
		return deviceTypeNo;
	}
	/**
	 * 设置：桩号
	 */
	public void setMilePost(String milePost) {
		this.milePost = milePost;
	}
	/**
	 * 获取：桩号
	 */
	public String getMilePost() {
		return milePost;
	}
	/**
	 * 设置：桩号对应的double值(单位:km)
	 */
	public void setMpValue(Integer mpValue) {
		this.mpValue = mpValue;
	}
	
	/**
	 * 获取：桩号对应的double值(单位:km)
	 */
	public Integer getMpValue() {
		return mpValue;
	}
	
	/**
	 * 设置：是否启用  (0停用，1在用)
	 */
	public void setUse(Integer use) {
		this.use = use;
	}
	
	/**
	 * 获取：是否启用  (0停用，1在用)
	 */
	public Integer getUse() {
		return use;
	}
	
	public Integer getDelStatus() {
		return delStatus;
	}

	public void setDelStatus(Integer delStatus) {
		this.delStatus = delStatus;
	}

	/**
	 * 设置：状态(0离线 1在线)
	 */
	public void setStatus(Integer status) {
		this.status = status;
	}
	/**
	 * 获取：状态(0离线 1在线)
	 */
	public Integer getStatus() {
		return status;
	}
	/**
	 * 设置：经度
	 */
	public void setLng(String lng) {
		this.lng = lng;
	}
	/**
	 * 获取：经度
	 */
	public String getLng() {
		return lng;
	}
	/**
	 * 设置：纬度
	 */
	public void setLat(String lat) {
		this.lat = lat;
	}
	/**
	 * 获取：纬度
	 */
	public String getLat() {
		return lat;
	}
	/**
	 * 设置：路段编号
	 */
	public void setRoadNo(Integer roadNo) {
		this.roadNo = roadNo;
	}
	/**
	 * 获取：路段编号
	 */
	public Integer getRoadNo() {
		return roadNo;
	}
	/**
	 * 设置：计算经纬度开关0关闭 1打开
	 */
	public void setComputeSwitch(Integer computeSwitch) {
		this.computeSwitch = computeSwitch;
	}
	/**
	 * 获取：计算经纬度开关0关闭 1打开
	 */
	public Integer getComputeSwitch() {
		return computeSwitch;
	}
	
	public Integer getFacilityTypeNo() {
		return facilityTypeNo;
	}
	
	public void setFacilityTypeNo(Integer facilityTypeNo) {
		this.facilityTypeNo = facilityTypeNo;
	}
	
	public List<Integer> getIds() {
		return ids;
	}
	
	public void setIds(List<Integer> ids) {
		this.ids = ids;
	}
	
	public String getTableName() {
		return tableName;
	}
	
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	
	public Integer getSourceId() {
		return sourceId;
	}
	
	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}
	
	public Integer getSort() {
		return sort;
	}
	
	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public String getSpUpdateTime() {
		return spUpdateTime;
	}

	public void setSpUpdateTime(String spUpdateTime) {
		this.spUpdateTime = spUpdateTime;
	}

	public String getRoadName() {
		return roadName;
	}

	public void setRoadName(String roadName) {
		this.roadName = roadName;
	}
}
