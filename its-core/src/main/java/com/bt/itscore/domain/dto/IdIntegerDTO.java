package com.bt.itscore.domain.dto;

import java.io.Serializable;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class IdIntegerDTO implements Serializable{
	private static final long serialVersionUID = 8093843553086342036L;
	@NotNull(message = "id不能为空")
	@Min(value = 1, message = "id必须是大于0的整数")
	private Integer id;
	public IdIntegerDTO() {
	}
	public IdIntegerDTO(Integer id) {
		this.id = id;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
}
