package com.bt.itscore.domain.vo;

public class DevicePowerVO extends DeviceVO {
	private String code; // 设备页面编号
	private String pageTypeCode;// 设备页面类型编号
	private String loadId; // 设备loadId
	private String deviceTypeCode;// 电力监控设备类型编号
	private Float deviceX;// 设备坐标x
	private Float deviceY;// 设备坐标y
	private Float sourceX;// 信号源坐标x
	private Float sourceY;// 信号源坐标y
	private Float width;// 宽度
	private Integer sourceId; // 源站ID，沿海1,罗城2,大化3,昭平4,灵山5

	private Float msgBoxX; // 保存底部文本框坐标
	private Float msgBoxY; // 保存底部文本框坐标

	private String pageTypeName; // 保存设备页面类型文字
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getPageTypeCode() {
		return pageTypeCode;
	}

	public void setPageTypeCode(String pageTypeCode) {
		this.pageTypeCode = pageTypeCode;
	}

	public String getLoadId() {
		return loadId;
	}

	public void setLoadId(String loadId) {
		this.loadId = loadId;
	}

	public String getDeviceTypeCode() {
		return deviceTypeCode;
	}

	public void setDeviceTypeCode(String deviceTypeCode) {
		this.deviceTypeCode = deviceTypeCode;
	}

	public Float getDeviceX() {
		return deviceX;
	}

	public void setDeviceX(Float deviceX) {
		this.deviceX = deviceX;
	}

	public Float getDeviceY() {
		return deviceY;
	}

	public void setDeviceY(Float deviceY) {
		this.deviceY = deviceY;
	}

	public Float getSourceX() {
		return sourceX;
	}

	public void setSourceX(Float sourceX) {
		this.sourceX = sourceX;
	}

	public Float getSourceY() {
		return sourceY;
	}

	public void setSourceY(Float sourceY) {
		this.sourceY = sourceY;
	}

	public Float getWidth() {
		return width;
	}

	public void setWidth(Float width) {
		this.width = width;
	}

	public Integer getSourceId() {
		return sourceId;
	}

	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}

	public Float getMsgBoxX() {
		return msgBoxX;
	}

	public void setMsgBoxX(Float msgBoxX) {
		this.msgBoxX = msgBoxX;
	}

	public Float getMsgBoxY() {
		return msgBoxY;
	}

	public void setMsgBoxY(Float msgBoxY) {
		this.msgBoxY = msgBoxY;
	}

	public String getPageTypeName() {
		return pageTypeName;
	}

	public void setPageTypeName(String pageTypeName) {
		this.pageTypeName = pageTypeName;
	}
}
