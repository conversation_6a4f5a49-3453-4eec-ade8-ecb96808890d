package com.bt.itscore.domain.vo;

/**
 * 设备-防雷
 */
public class DeviceLightprotVO extends DeviceVO {
	private String code; // 设备唯一编码
	private String url;// 平台url-ip:port
	private Integer factory;// 厂家，1-上海宽永v1接口，2-上海宽永v2接口,3-上海雷迅串口，4-上海雷迅接口
	private String userKey;// 密钥
	private String deviceType;// 设备类型
	private String hostIp; // 主机地址
	private Integer port;// 端口号
	private Integer slaveId;// 端口号

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Integer getFactory() {
		return factory;
	}

	public void setFactory(Integer factory) {
		this.factory = factory;
	}

	public String getUserKey() {
		return userKey;
	}

	public void setUserKey(String userKey) {
		this.userKey = userKey;
	}

	public String getDeviceType() {
		return deviceType;
	}

	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}

	public String getHostIp() {
		return hostIp;
	}

	public void setHostIp(String hostIp) {
		this.hostIp = hostIp;
	}

	public Integer getPort() {
		return port;
	}

	public void setPort(Integer port) {
		this.port = port;
	}

	public Integer getSlaveId() {
		return slaveId;
	}

	public void setSlaveId(Integer slaveId) {
		this.slaveId = slaveId;
	}

}