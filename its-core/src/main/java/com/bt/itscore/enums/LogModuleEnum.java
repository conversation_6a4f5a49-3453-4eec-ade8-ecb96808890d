package com.bt.itscore.enums;

/**
 * <AUTHOR>
 * @date 2022年7月13日 下午4:48:41
 * @Description 日志类型，操作类型
 */
public enum LogModuleEnum {
    TYPE_1("设施", 1),
    TYPE_2("设备", 2),
    TYPE_3("用户", 3),
    TYPE_4("组织机构", 4),
    TYPE_5("角色", 5),
    TYPE_6("电力监控", 6);
    private String name;  
    private int index;
    private LogModuleEnum(String name, int index) {  
        this.name = name;
        this.index = index;  
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public int getIndex() {
        return index;
    }
    public void setIndex(int index) {
        this.index = index;
    }
    // 普通方法  
    public static String getName(int index) {  
        for (LogModuleEnum e : LogModuleEnum.values()) {  
            if (e.getIndex() == index) {  
                return e.name;  
            }  
        }  
        return null;  
    }
}
