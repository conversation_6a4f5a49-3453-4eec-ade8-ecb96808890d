package com.bt.itscore.enums;

public enum LightprotTypeEnum {
	TYPE_11("雷击次数", 11, "上海宽永V1", "次"), 
	TYPE_12("遥信", 12, "上海宽永V1", "1:闭合,0:打开"),
	TYPE_13("雷击电流", 13, "上海宽永V1", "kA"),
	TYPE_14("泄漏电流", 14, "上海宽永V1", "A"), 
	TYPE_15("接地阻抗", 15, "上海宽永V1", "Ω"), 
	TYPE_21("遥信值", 21, "上海宽永V2", "0:正常,1:报警"),
	TYPE_22("空开值", 22, "上海宽永V2", "0:正常,1:报警"), 
	TYPE_23("丢地值", 23, "上海宽永V2", "0:正常,1:报警"), 
	TYPE_24("雷击次数", 24, "上海宽永V2", "次"),
	//TYPE_25("泄漏电流", 25, "上海宽永V2", ""),
	TYPE_26("寿命值", 26, "上海宽永V2", ""), 
	TYPE_27("温度", 27, "上海宽永V2", ""),
	TYPE_28("采集次数", 28, "上海宽永V2", ""),
	TYPE_29("采集频率", 29, "上海宽永V2", ""), 
	TYPE_30("地网阻值", 30, "上海宽永V2", ""),
	TYPE_31("次数", 31, "上海宽永V2", ""), 
	TYPE_32("峰值", 32, "上海宽永V2", ""), 
	//TYPE_33("极性", 33, "上海宽永V2", ""),
	TYPE_100("环境温度", 100, "上海雷迅", "℃"),
	TYPE_101("设备地址", 101, "上海雷迅", ""), 
	TYPE_102("雷击计数", 102, "上海雷迅", "次"),
	TYPE_103("最近雷击时间", 103, "上海雷迅", ""), 
	TYPE_104("系统时间", 104, "上海雷迅", ""),
	TYPE_105("SPD温度", 105, "上海雷迅", "℃"),
	TYPE_106("雷电流大小", 106, "上海雷迅", "KA"),
	TYPE_107("雷电流极性", 107, "上海雷迅", ""),	
	TYPE_108("接地电阻", 108, "上海雷迅", "欧姆"),
	TYPE_109("ID地址", 109, "上海雷迅", ""),
	TYPE_110("报警值", 110, "上海雷迅", "欧姆"),
	TYPE_111("报警状态", 111, "上海雷迅", "0:正常,1:报警"),	
	TYPE_112("实时漏电流值", 112, "上海雷迅", "A"),
	TYPE_113("在线天数", 113, "上海雷迅", "天"),
	TYPE_114("设备时间", 114, "上海雷迅", "");
	private String name; // 名称
	private int index; // 编码
	private String factory; // 厂家
	private String unit; //数值含义

	private LightprotTypeEnum(String name, int index, String factory,String unit) {
		this.name = name;
		this.index = index;
		this.factory = factory;
		this.unit=unit;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getFactory() {
		return factory;
	}

	public void setFactory(String factory) {
		this.factory = factory;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	// 获取名称
	public static LightprotTypeEnum getName(int index) {
		for (LightprotTypeEnum e : LightprotTypeEnum.values()) {
			if (e.getIndex() == index) {
				return e;
			}
		}
		return null;
	}

	// 获取类型
	public static LightprotTypeEnum getIndex(String name) {
		for (LightprotTypeEnum e : LightprotTypeEnum.values()) {
			if (name.contains(e.getName())) {
				return e;
			}
		}
		return null;
	}
}
