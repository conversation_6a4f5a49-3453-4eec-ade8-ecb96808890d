package com.bt.itscore.enums;

/**
 * <AUTHOR>
 * @date 2023年3月1日 下午4:14:56
 * @Description 千方96333的业务类型（01投诉举报、02信息咨询、03意见建议、04应急救援）
 */
public enum ServiceTypeEnum {
	TS("TS", "01"),
	ZX("ZX", "02"),
	JY("JY", "03"),
    DD("DD", "04");
	private String name;
	private String value;

	private ServiceTypeEnum(String name, String value) {
		this.name = name;
		this.value = value;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

}
