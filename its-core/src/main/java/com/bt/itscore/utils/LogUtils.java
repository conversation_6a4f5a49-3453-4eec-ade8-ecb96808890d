package com.bt.itscore.utils;

import javax.servlet.http.HttpServletRequest;

import org.aspectj.lang.JoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.bt.itscore.domain.dto.LogOperateDTO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.google.gson.Gson;
/**
 * <AUTHOR>
 * @date 2022年7月15日 下午5:43:31
 * @Description 日志工具类
 */
public class LogUtils {

	private final static Logger LOGGER = LoggerFactory.getLogger(LogUtils.class);

	public static void appendParams(LogOperateDTO dto, JoinPoint joinPoint) {
		Object[] args = joinPoint.getArgs();
		String params = "";
		for (Object arg : args) {
			Class<? extends Object> class1 = arg.getClass();
			if(class1 == org.springframework.validation.BeanPropertyBindingResult.class) {
				continue;
			}
			if (arg instanceof HttpServletRequest) {
				continue;
			}
			String json = new Gson().toJson(arg);
			String keyword = "\"password\":\"";
			int index = json.indexOf(keyword);
			if(index != -1) {
				String substring = json.substring(index + keyword.length());
				json = json.replaceAll("\"" + substring.substring(0, substring.indexOf("\"") + 1), "\"***\"");
//				json.replaceAll(keyword + substring.substring(0, substring.indexOf("\"") + 1), replacement);
			}
			params += json + ";";
		}
		LOGGER.info(params);
		dto.setParams(params);
	}
	
	@SuppressWarnings("rawtypes")
	public static void appendRemark(LogOperateDTO dto, Object res, String operate) {
		String moduleName = dto.getModuleName();
		String remark = "";
		Class retClass = res.getClass();
		if(retClass == com.bt.itscore.domain.vo.ResponseVO.class) {
			ResponseVO ret = (ResponseVO)res;
			if(ret.getCode() == 1) {
				dto.setSuccess(1);
				remark = operate + moduleName + "成功。";
			} else {
				dto.setSuccess(0);
				remark = operate + moduleName + "失败。";
			}
			LOGGER.info("{}", remark);
			dto.setRemark(remark);
		}
	}

}
