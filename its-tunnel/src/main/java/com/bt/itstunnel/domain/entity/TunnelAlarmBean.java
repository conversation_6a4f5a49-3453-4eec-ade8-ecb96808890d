package com.bt.itstunnel.domain.entity;

public class TunnelAlarmBean {
	private String websocketType = "tunnelAlarmChange";
	private String alarmId;
	private String roadNo; // 所属路段
	private String roadName;// 路段名称
	private String facilityNo;// 设施ID
	private String facilityName;// 隧道名称
	private String deviceName; // 设备名称
	private String deviceId;// 设备id
	private String type;// 外部接口报警类型：501（特殊隧道报警），502（紧急电话报警），503（手动按钮报警），504（感温光栅、烟感、双波长报警） ，505 COVI报警
						// ，506:视频事件检测 ，507：雷达报警事件，508：二氧化氮报警 ，509：亮度检测器报警，510：蓄电池检测报警，511：声光报警火灾声光报警探测器
						// ，512 高低位水池报警， 513:两危一化报警， 514:消防水渠抽水报警 ，515:车道指示器、红绿灯熄灭报警,516:风机长时间运行报警
	private String detail;// 报警详情
	private String value;// 报警值
	private Long createTime;// 报警时间
	private String planId;// 联动预案ID
	private String cameraId;// 联动摄像机ID
	private String milePost;// 触发设备桩号

	public String getWebsocketType() {
		return websocketType;
	}

	public String getAlarmId() {
		return alarmId;
	}

	public void setAlarmId(String alarmId) {
		this.alarmId = alarmId;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getDetail() {
		return detail;
	}

	public void setDetail(String detail) {
		this.detail = detail;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public Long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public String getPlanId() {
		return planId;
	}

	public void setPlanId(String planId) {
		this.planId = planId;
	}

	public String getCameraId() {
		return cameraId;
	}

	public void setCameraId(String cameraId) {
		this.cameraId = cameraId;
	}

	public String getMilePost() {
		return milePost;
	}

	public void setMilePost(String milePost) {
		this.milePost = milePost;
	}

	public String getFacilityName() {
		return facilityName;
	}

	public void setFacilityName(String facilityName) {
		this.facilityName = facilityName;
	}

	public String getDeviceName() {
		return deviceName;
	}

	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}

    public String getFacilityNo() {
        return facilityNo;
    }

    public void setFacilityNo(String facilityNo) {
        this.facilityNo = facilityNo;
    }

	public String getRoadNo() {
		return roadNo;
	}

	public void setRoadNo(String roadNo) {
		this.roadNo = roadNo;
	}

	public String getRoadName() {
		return roadName;
	}

	public void setRoadName(String roadName) {
		this.roadName = roadName;
	}

	
}
