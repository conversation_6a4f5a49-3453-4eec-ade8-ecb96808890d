package com.bt.itstunnel.domain.entity;

import com.bt.itstunnel.common.CommonFunction;

/**
 * 隧道页面布局展示信息
 * <AUTHOR>
 *
 */
public class TunnePlcBean {
	private String devicetypeNo= "5"; //隧道设备类型
	private String id;// 设备Id
	private String roadNo;// 路段ID
	private String roadName;// 路段名称
	private String facilityNo;// 隧道Id
	private String facilityName;// 隧道名称
	private String directionNo; // 路段方向
	private String directionName; // 方向名称
	private String deviceId;// 主表Id
	private String deviceCode;// 设备编码
	private String deviceName;// 设备名称
	private String typeId;// 设备类型Id
	private String typeCode;// 设备类型编号
	private String typeName;// 设备类型名称
	private String milePost;// 设备桩号
	private String plcCode;// 所属PLC
	private String ipAddress;// IP地址
	private String deviceState;// 设备状态 -1/err：故障，空值：初始化(即离线状态)，不为空:工作状态
	private String otherState;// 要显示的其他设备状态 显示亮度仪状态
	private Integer line; // 判断是否是左线、右线 （1：左线，2：右线,中间：0）
	private String laneType; // 车道类型（1-超车道、2-行车道、3-应急车道）
	private String lightType; // 灯类型：1：入口引道灯 2：入口加强灯 3、过渡加强灯 4、基本段 5、出口加强段6、出口引道灯 7、应急灯;调光类型
	private String imgUrl;// 展示图标路径
	private String titleMens;// 页面信息展示
	private Double xRatio; // x轴百分比
	private Double yRatio;// y轴百分比
	private String imgArea;// 图标位置 10：小桩号洞口,11：洞内 12：大桩号洞口
	private String groupNum;// 风机、紧急电话分组、雷达分组
	
	public String getDevicetypeNo() {
		return devicetypeNo;
	}

	public void setDevicetypeNo(String devicetypeNo) {
		this.devicetypeNo = devicetypeNo;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getRoadNo() {
		return roadNo;
	}

	public void setRoadNo(String roadNo) {
		this.roadNo = roadNo;
	}

	public String getRoadName() {
		return roadName;
	}

	public void setRoadName(String roadName) {
		this.roadName = roadName;
	}

	public String getFacilityNo() {
		return facilityNo;
	}

	public void setFacilityNo(String facilityNo) {
		this.facilityNo = facilityNo;
	}

	public String getFacilityName() {
		return facilityName;
	}

	public void setFacilityName(String facilityName) {
		this.facilityName = facilityName;
	}

	public String getDirectionNo() {
		return directionNo;
	}

	public void setDirectionNo(String directionNo) {
		this.directionNo = directionNo;
	}

	public String getDirectionName() {
		return directionName;
	}

	public void setDirectionName(String directionName) {
		this.directionName = directionName;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public String getDeviceCode() {
		return deviceCode;
	}

	public void setDeviceCode(String deviceCode) {
		this.deviceCode = deviceCode;
	}

	public String getDeviceName() {
		return deviceName;
	}

	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}

	public String getTypeId() {
		return typeId;
	}

	public void setTypeId(String typeId) {
		this.typeId = typeId;
	}

	public String getTypeCode() {
		return typeCode;
	}

	public void setTypeCode(String typeCode) {
		this.typeCode = typeCode;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public String getMilePost() {
		return milePost;
	}

	public void setMilePost(String milePost) {
		this.milePost = milePost;
	}

	public String getPlcCode() {
		return plcCode;
	}

	public void setPlcCode(String plcCode) {
		this.plcCode = plcCode;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	public String getDeviceState() {
		return deviceState;
	}

	public void setDeviceState(String deviceState) {
		this.deviceState = deviceState;
	}

	public Integer getLine() {
		return line;
	}

	public void setLine(Integer line) {
		this.line = line;
	}

	public String getLaneType() {
		return laneType;
	}

	public void setLaneType(String laneType) {
		this.laneType = laneType;
	}

	public String getLightType() {
		return lightType;
	}

	public void setLightType(String lightType) {
		this.lightType = lightType;
	}

	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public String getTitleMens() {
		titleMens = CommonFunction.getDeviceTitileMens(facilityName, deviceCode, deviceName, deviceState, typeCode,
				typeName, milePost, ipAddress, plcCode);
		return titleMens;
	}

	public void setTitleMens(String titleMens) {
		this.titleMens = titleMens;
	}

	public Double getxRatio() {
		return xRatio;
	}

	public void setxRatio(Double xRatio) {
		this.xRatio = xRatio;
	}

	public Double getyRatio() {
		return yRatio;
	}

	public void setyRatio(Double yRatio) {
		this.yRatio = yRatio;
	}

	public String getImgArea() {
		return imgArea;
	}

	public void setImgArea(String imgArea) {
		this.imgArea = imgArea;
	}

	public String getGroupNum() {
		return groupNum;
	}

	public void setGroupNum(String groupNum) {
		this.groupNum = groupNum;
	}

	public String getOtherState() {
		return otherState;
	}

	public void setOtherState(String otherState) {
		this.otherState = otherState;
	}

}
