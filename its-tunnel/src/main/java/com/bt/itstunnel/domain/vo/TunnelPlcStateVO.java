package com.bt.itstunnel.domain.vo;

public class TunnelPlcStateVO {
	private String id;// int ID
	private String typeId;//int,设备类型Id
	private String typeCode;// 设备类型编号
	private String typeName;//设备类型名称
	private String state;// 对应状态  :0：离线 1：在线（正常）  ：2：报警(根据设备类型状态而定)
	private String imgLeft;//上行线图标
	private String imgRight;//下行线图标
	private String useFlag; //是否使用 ，0：否，1：启用	
	private Integer width;// int NOT NULL,设备类别图标宽
	private Integer height;// int NOT NULL,图标高
	private String remark;// varchar(200) NULL，备注
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getTypeId() {
		return typeId;
	}
	public void setTypeId(String typeId) {
		this.typeId = typeId;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getImgLeft() {
		return imgLeft;
	}
	public void setImgLeft(String imgLeft) {
		this.imgLeft = imgLeft;
	}
	public String getImgRight() {
		return imgRight;
	}
	public void setImgRight(String imgRight) {
		this.imgRight = imgRight;
	}
	public String getUseFlag() {
		return useFlag;
	}
	public void setUseFlag(String useFlag) {
		this.useFlag = useFlag;
	}
	public Integer getWidth() {
		return width;
	}
	public void setWidth(Integer width) {
		this.width = width;
	}
	public Integer getHeight() {
		return height;
	}
	public void setHeight(Integer height) {
		this.height = height;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getTypeCode() {
		return typeCode;
	}
	public void setTypeCode(String typeCode) {
		this.typeCode = typeCode;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	
}
