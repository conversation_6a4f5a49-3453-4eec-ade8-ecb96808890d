package com.bt.itstunnel.domain.vo;

public class TunnelOperateLogVO {
	private String id;// 操作类型编码
	private String deviceId;//设备id
	private String preState;//操作前状态
	private String operateName;// 操作描述
	private Long operateTime;// 操作时间	
	private String operater;// 操作人
	private String command;// 操作命令
	private int sendFlag;// 是否已发送 0：未发送 1：已发送
	private String tunnelId;// 隧道Id
	private String milePost;//触发设备桩号
	private String typeId;// int 设备类型Id
	private String typeName;// 设备类型名称	
	private String tunnelName;  //设备所属隧道名称
	private String deviceName;  //设备名称
	private String roadName;  //设备所属隧道名称
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getDeviceId() {
		return deviceId;
	}
	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}
	public String getPreState() {
		return preState;
	}
	public void setPreState(String preState) {
		this.preState = preState;
	}
	public String getOperateName() {
		return operateName;
	}
	public void setOperateName(String operateName) {
		this.operateName = operateName;
	}
	public Long getOperateTime() {
		return operateTime;
	}
	public void setOperateTime(Long operateTime) {
		this.operateTime = operateTime;
	}
	public String getOperater() {
		return operater;
	}
	public void setOperater(String operater) {
		this.operater = operater;
	}
	public String getCommand() {
		return command;
	}
	public void setCommand(String command) {
		this.command = command;
	}
	public int getSendFlag() {
		return sendFlag;
	}
	public void setSendFlag(int sendFlag) {
		this.sendFlag = sendFlag;
	}
	public String getTunnelId() {
		return tunnelId;
	}
	public void setTunnelId(String tunnelId) {
		this.tunnelId = tunnelId;
	}
	public String getMilePost() {
		return milePost;
	}
	public void setMilePost(String milePost) {
		this.milePost = milePost;
	}
	public String getTypeId() {
		return typeId;
	}
	public void setTypeId(String typeId) {
		this.typeId = typeId;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public String getTunnelName() {
		return tunnelName;
	}
	public void setTunnelName(String tunnelName) {
		this.tunnelName = tunnelName;
	}
	public String getDeviceName() {
		return deviceName;
	}
	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}
	public String getRoadName() {
		return roadName;
	}
	public void setRoadName(String roadName) {
		this.roadName = roadName;
	}
	
	
	
}
