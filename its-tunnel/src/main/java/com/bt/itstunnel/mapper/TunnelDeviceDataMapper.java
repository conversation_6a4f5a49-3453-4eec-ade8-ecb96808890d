package com.bt.itstunnel.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.bt.itstunnel.domain.dto.CommonParamsDTO;
import com.bt.itstunnel.domain.dto.TunnelDeviceDataDTO;
import com.bt.itstunnel.domain.vo.DataReportVO;
import com.bt.itstunnel.domain.vo.TunnelDeviceDataVO;

@Mapper
public interface TunnelDeviceDataMapper {
	int addCoviList(List<TunnelDeviceDataDTO> list);// covi

	int addWindDetectorList(List<TunnelDeviceDataDTO> list);// 风速风向

	int addLuminanceList(List<TunnelDeviceDataDTO> list);// 亮度监测

	int addLightingList(List<TunnelDeviceDataDTO> list);// 调光电流

	int addNo2List(List<TunnelDeviceDataDTO> list);// 二氧化氮

	int addWaterLeveList(List<TunnelDeviceDataDTO> list);// 水位计

	List<TunnelDeviceDataVO> selectCoviList(CommonParamsDTO commonParamsDTO);// covi

	List<TunnelDeviceDataVO> selectWindDetectorList(CommonParamsDTO commonParamsDTO);// 风速风向

	List<TunnelDeviceDataVO> selectLuminanceList(CommonParamsDTO commonParamsDTO);// 亮度监测

	List<TunnelDeviceDataVO> selectLightingList(CommonParamsDTO commonParamsDTO);

	List<TunnelDeviceDataVO> selectNo2List(CommonParamsDTO commonParamsDTO);

	List<TunnelDeviceDataVO> selectWaterLeveList(CommonParamsDTO commonParamsDTO);

	int countAllByTable(CommonParamsDTO commonParamsDTO);

	List<DataReportVO> selectDataReprot(CommonParamsDTO commonParamsDTO);
	
	List<DataReportVO> selectAllDataReprot();
	
	int updateList(List<DataReportVO> list);

}
