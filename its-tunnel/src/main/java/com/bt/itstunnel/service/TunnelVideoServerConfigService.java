package com.bt.itstunnel.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itstunnel.domain.dto.TunnelVideoServerConfigDTO;
import com.bt.itstunnel.domain.vo.TunnelVideoServerConfigVO;
import com.bt.itstunnel.mapper.TunnelVideoServerConfigMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

@Service("tunnelVideoServerConfigService")
public class TunnelVideoServerConfigService {

	@Autowired
	private TunnelVideoServerConfigMapper tunnelVideoServerConfigMapper;

	public PageInfo<TunnelVideoServerConfigVO> page(TunnelVideoServerConfigDTO tunnelVideoServerConfigDTO,
			PageDTO pageDTO) {
		PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
		List<TunnelVideoServerConfigVO> list = tunnelVideoServerConfigMapper.selectList(tunnelVideoServerConfigDTO);
		return new PageInfo<>(list);
	}

	public List<TunnelVideoServerConfigVO> getTunnelVideoServerConfigList(
			TunnelVideoServerConfigDTO tunnelVideoServerConfigDTO) {
		return tunnelVideoServerConfigMapper.selectList(tunnelVideoServerConfigDTO);
	}

	public boolean add(TunnelVideoServerConfigDTO tunnelVideoServerConfigDTO) {
		return tunnelVideoServerConfigMapper.add(tunnelVideoServerConfigDTO) > 0;
	}

	public boolean saveVideoServerConfig(TunnelVideoServerConfigDTO tunnelVideoServerConfigDTO) {
		String serverId = tunnelVideoServerConfigDTO.getServerId();
		tunnelVideoServerConfigMapper.deleteByServerId(serverId); // 删除已配置的信息
		List<String> ids = tunnelVideoServerConfigDTO.getIds();
		for (String tunnelId : ids) {
			TunnelVideoServerConfigDTO videoServerConfig = new TunnelVideoServerConfigDTO();
			videoServerConfig.setTunnelId(tunnelId);
			videoServerConfig.setServerId(serverId);
			tunnelVideoServerConfigMapper.add(videoServerConfig);
		}
		return true;
	}
}
