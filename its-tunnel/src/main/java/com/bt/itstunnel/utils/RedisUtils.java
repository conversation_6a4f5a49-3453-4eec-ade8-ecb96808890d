package com.bt.itstunnel.utils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
@Component
public class RedisUtils {
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;

	/**
	 * 设置有效时间
	 *
	 * @param key
	 *            Redis键
	 * @param timeout
	 *            超时时间
	 * @return true=设置成功；false=设置失败
	 */
	public boolean expire(final String key, final long timeout) {

		return expire(key, timeout, TimeUnit.SECONDS);
	}

	/**
	 * 设置有效时间
	 *
	 * @param key
	 *            Redis键
	 * @param timeout
	 *            超时时间
	 * @param unit
	 *            时间单位
	 * @return true=设置成功；false=设置失败
	 */
	public boolean expire(final String key, final long timeout, final TimeUnit unit) {

		Boolean flag = redisTemplate.expire(key, timeout, unit);
		return flag != null && flag;
	}

	/**
	 * 删除单个key
	 *
	 * @param key
	 *            键
	 * @return true=删除成功；false=删除失败
	 */
	public boolean del(final String key) {

		Boolean flag = redisTemplate.delete(key);
		return flag != null && flag;
	}

	/**
	 * 删除多个key
	 *
	 * @param keys
	 *            键集合
	 * @return 成功删除的个数
	 */
	public long del(final Collection<String> keys) {

		Long flag = redisTemplate.delete(keys);
		return flag == null ? 0 : flag;
	}

	/**
	 * 存入普通对象
	 *
	 * @param key
	 *            Redis键
	 * @param value
	 *            值
	 */
	public void set(final String key, final Object value) {

		// 普通对象 不加失效时间的话 默认有效时间1分钟
		redisTemplate.opsForValue().set(key, value, 1, TimeUnit.MINUTES);
	}

	/**
	 * 存入普通对象
	 *
	 * @param key
	 *            键
	 * @param value
	 *            值
	 * @param timeout
	 *            有效期，单位秒
	 */
	public void set(final String key, final Object value, final long timeout) {

		redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
	}

	/**
	 * 获取普通对象
	 *
	 * @param key
	 *            键
	 * @return 对象
	 */
	public Object get(final String key) {

		return redisTemplate.opsForValue().get(key);
	}

	/**
	 * 往Hash中存入数据
	 *
	 * @param key
	 *            Redis键
	 * @param hKey
	 *            Hash键
	 * @param value
	 *            值
	 */
	public void hPut(final String key, final String hKey, final Object value) {

		redisTemplate.opsForHash().put(key, hKey, value);
	}

	/**
	 * 往Hash中存入多个数据
	 *
	 * @param key
	 *            Redis键
	 * @param values
	 *            Hash键值对
	 */
	public void hPutAll(final String key, final Map<String, Object> values) {

		redisTemplate.opsForHash().putAll(key, values);
	}

	/**
	 * 获取Hash中的数据
	 *
	 * @param key
	 *            Redis键
	 * @param hKey
	 *            Hash键
	 * @return Hash中的对象
	 */
	public Object hGet(final String key, final String hKey) {

		return redisTemplate.opsForHash().get(key, hKey);
	}

	/**
	 * 获取多个Hash中的数据
	 *
	 * @param key
	 *            Redis键
	 * @param hKeys
	 *            Hash键集合
	 * @return Hash对象集合
	 */
	public List<Object> hMultiGet(final String key, final Collection<Object> hKeys) {

		return redisTemplate.opsForHash().multiGet(key, hKeys);
	}

	/**
	 * 往Set中存入数据
	 *
	 * @param key
	 *            Redis键
	 * @param values
	 *            值
	 * @return 存入的个数
	 */
	public long sSet(final String key, final Object... values) {
		Long count = redisTemplate.opsForSet().add(key, values);
		return count == null ? 0 : count;
	}

	/**
	 * 删除Set中的数据
	 *
	 * @param key
	 *            Redis键
	 * @param values
	 *            值
	 * @return 移除的个数
	 */
	public long sDel(final String key, final Object... values) {
		Long count = redisTemplate.opsForSet().remove(key, values);
		return count == null ? 0 : count;
	}

	/**
	 * 往List中存入数据
	 *
	 * @param key
	 *            Redis键
	 * @param value
	 *            数据
	 * @return 存入的个数
	 */
	public long lPush(final String key, final Object value) {
		Long count = redisTemplate.opsForList().rightPush(key, value);
		return count == null ? 0 : count;
	}

	/**
	 * 往List中存入多个数据
	 *
	 * @param key
	 *            Redis键
	 * @param values
	 *            多个数据
	 * @return 存入的个数
	 */
	public long lPushAll2(final String key, final Collection<Object> values) {
		Long count = redisTemplate.opsForList().rightPushAll(key, values);
		return count == null ? 0 : count;
	}

	/**
	 * 往List中存入多个数据
	 *
	 * @param key
	 *            Redis键
	 * @param values
	 *            多个数据
	 * @return 存入的个数
	 */
	public long lPushAll(final String key, final Object... values) {
		Long count = 0L;
		try {
			redisTemplate.opsForList().rightPushAll(key, values);
			count++;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return count == null ? 0 : count;
	}

	/**
	 * 从List中获取begin到end之间的元素
	 *
	 * @param key
	 *            Redis键
	 * @param start
	 *            开始位置
	 * @param end
	 *            结束位置（start=0，end=-1表示获取全部元素）
	 * @return List对象
	 */
	public List<Object> lGet(final String key, final int start, final int end) {
		return redisTemplate.opsForList().range(key, start, end);
	}

}
