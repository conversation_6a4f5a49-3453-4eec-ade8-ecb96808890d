package com.bt.itsmax.constants;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 
 * @Description: 定义公共静态参数
 * <AUTHOR>
 * @date 2025年1月10日 上午10:00:59
 *
 */
public class CommonParam {
	public static final int[] QB_NUMBER = { 7, 8, 9 }; // 南宁-7，钦州-8，北海-9

	public static List<Integer> getQbSourceIds() {
		List<Integer> sourceIds = Arrays.stream(QB_NUMBER).boxed().collect(Collectors.toList());
		return sourceIds;
	}

	// 南宁-7，钦州-8，北海-9
	public static final String[] QB_ORGIDS = { "37a6e460-808c-4eab-aa2b-03d575f6ca65",
			"3cfa77a3-3825-43a8-821e-ba678073af70", "edeb21d6-daea-4c81-b2fc-1e2836a4f80b" };

	public static List<String> getQbOrgIds() {
		List<String> orgIds = Arrays.stream(QB_ORGIDS).collect(Collectors.toList());
		return orgIds;
	}

	// 南宁-7，钦州-8，北海-9
	public static final String[] QB_ORGNAMES = { "南宁", "钦州", "北海" };

	public static List<String> getQbOrgNames() {
		List<String> orgNames = Arrays.stream(QB_ORGNAMES).collect(Collectors.toList());
		return orgNames;
	}
}
