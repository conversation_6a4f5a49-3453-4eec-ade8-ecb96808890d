package com.bt.itsmax.domain.dto;

import javax.validation.constraints.NotBlank;

public class WebDTO {
	private String id;// 主键id
	@NotBlank(message = "门户名称不能为空")
	private String webName;// 门户名称
	@NotBlank(message = "门户网址不能为空")
	private String webUrl;// 门户网址
	private String createTime;// 创建时间
	private String sort;// 排序
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getWebName() {
		return webName;
	}
	public void setWebName(String webName) {
		this.webName = webName;
	}
	public String getWebUrl() {
		return webUrl;
	}
	public void setWebUrl(String webUrl) {
		this.webUrl = webUrl;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getSort() {
		return sort;
	}
	public void setSort(String sort) {
		this.sort = sort;
	}
	
}
