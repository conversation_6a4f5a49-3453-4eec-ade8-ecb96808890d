package com.bt.itsmax.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bt.itsmax.domain.dto.CommonParamDTO;
import com.bt.itsmax.domain.dto.DeviceDTO;
import com.bt.itsmax.domain.vo.DeviceCameraVO;
import com.bt.itsmax.domain.vo.DeviceGantryVO;
import com.bt.itsmax.domain.vo.DeviceStatVO;

@Mapper
public interface DeviceMapper {

	List<DeviceGantryVO> selectDeviceGantryList(DeviceDTO dto);

	List<DeviceGantryVO> selectDeviceGantryValueList(DeviceDTO dto);

	List<DeviceCameraVO> selectQbCamera(@Param("sourceIds") List<Integer> sourceIds);

	List<DeviceStatVO> statDevice(CommonParamDTO dto);

	List<DeviceStatVO> statDeviceCamera(CommonParamDTO dto);

	List<DeviceStatVO> statDeviceCharge(@Param("stationIds") List<String> stationIds);
	
	List<String> selectDevicePositionIdsByRoleIds(Map<String, Object> map);
}
