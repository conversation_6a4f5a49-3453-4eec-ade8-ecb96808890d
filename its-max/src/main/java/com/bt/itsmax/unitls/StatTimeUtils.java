package com.bt.itsmax.unitls;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import com.bt.itscore.utils.TimeUtils;

/**
 * 
 * @Description: 统计年度，季度，月度工具类
 * <AUTHOR>
 * @date 2024年8月30日 上午10:53:46
 *
 */
public class StatTimeUtils {

	/**
	 * 生成两个年月之间的所有年月的字符串列表
	 * 
	 * @param start 起始年月
	 * @param end 结束年月
	 * @return 包含起始年月和结束年月在内的年月字符串列表
	 */
	public static List<String> generateYearMonthsBetween(YearMonth start, YearMonth end) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

		return StreamSupport.stream(new YearMonthIterable(start, end).spliterator(), false)
				.map(ym -> ym.format(formatter)).collect(Collectors.toList());
	}

	/**
	 * 一个可迭代的类，用于生成两个年月之间的所有年月
	 */
	static class YearMonthIterable implements Iterable<YearMonth> {
		private final YearMonth start;
		private final YearMonth end;

		public YearMonthIterable(YearMonth start, YearMonth end) {
			this.start = start;
			this.end = end;
		}

		@Override
		public java.util.Iterator<YearMonth> iterator() {
			return new java.util.Iterator<YearMonth>() {
				private YearMonth current = start;

				@Override
				public boolean hasNext() {
					return !current.isAfter(end);
				}

				@Override
				public YearMonth next() {
					if (!hasNext()) {
						throw new java.util.NoSuchElementException();
					}
					YearMonth result = current;
					current = current.plusMonths(1);
					return result;
				}
			};
		}
	}

	/**
	 * 生成两个整数之间的所有整数列表
	 * @param start 起始整数
	 * @param end 结束整数
	 * @return 包含起始整数和结束整数在内的整数列表
	 */
	public static List<Integer> generateIntegersBetween(int start, int end) {
		List<Integer> integers = new ArrayList<>();
		for (int i = start; i <= end; i++) {
			integers.add(i);
		}
		return integers;
	}

	/**
	 * @描述 获取指定季度的年月
	 * @param year
	 * @param quarter
	 * @return
	 */
	public static List<String> getQuarterMonth(int year, int quarter) {

		YearMonth start = YearMonth.of(year, 1);
		YearMonth end = YearMonth.of(year, 12);
		switch (quarter) {
		case 1:
			start = YearMonth.of(year, 1); // 起始年月
			end = YearMonth.of(year, 3); // 结束年月
			break;
		case 2:
			start = YearMonth.of(year, 4); // 起始年月
			end = YearMonth.of(year, 6); // 结束年月
			break;
		case 3:
			start = YearMonth.of(year, 7); // 起始年月
			end = YearMonth.of(year, 9); // 结束年月
			break;
		case 4:
			start = YearMonth.of(year, 10); // 起始年月
			end = YearMonth.of(year, 12); // 结束年月
			break;
		default:
			break;
		}
		List<String> quarterMonth = generateYearMonthsBetween(start, end);
		return quarterMonth;
	}

	/**
	 * @描述 获取指定季度的年月
	 * @param year：当前年份
	 * @param quarter:当前季度
	 * @return
	 */
	public static List<String> getLastQuarterSatrtAndEndDay(int year, int quarter) {

		YearMonth start = YearMonth.of(year, 1);
		YearMonth end = YearMonth.of(year, 12);
		switch (quarter) {
		case 1:
			start = YearMonth.of(year - 1, 10); // 起始年月
			end = YearMonth.of(year - 1, 12); // 结束年月
			break;
		case 2:
			start = YearMonth.of(year, 1); // 起始年月
			end = YearMonth.of(year, 3); // 结束年月
			break;
		case 3:
			start = YearMonth.of(year, 4); // 起始年月
			end = YearMonth.of(year, 6); // 结束年月
			break;
		case 4:
			start = YearMonth.of(year, 7); // 起始年月
			end = YearMonth.of(year, 9); // 结束年月
			break;
		}
		List<String> list = new ArrayList<String>();
		list.add(start.atDay(1).toString());
		list.add(end.atEndOfMonth().toString());
		return list;
	}

	public static List<String> getYearSatrtAndEndDay(int year) {
		YearMonth start = YearMonth.of(year, 1);
		YearMonth end = YearMonth.of(year, 12);
		List<String> list = new ArrayList<String>();
		list.add(start.atDay(1).toString());
		list.add(end.atEndOfMonth().toString());
		return list;
	}

	/**
	 * 获取指定年月前n个月的年月列表。
	 *
	 * @param yearMonth 当前年月
	 * @param n         要获取的前几个月
	 * @return 包含前几个月年月的列表
	 */
	public static List<String> getPreviousYearMonths(YearMonth yearMonth, int n) {
		List<String> yearMonths = new ArrayList<>();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

		for (int i = 0; i <= n; i++) { // 包括当前年月
			YearMonth current = yearMonth.minusMonths(i);
			yearMonths.add(current.format(formatter));
		}

		return yearMonths;
	}

	/**
	 * @描述 根据年份获取12个月
	 * @param year
	 * @return
	 */
	public static List<String> getCurrentYearMonth(int year) {
		YearMonth start = YearMonth.of(year, 1);
		YearMonth end = YearMonth.of(year, 12);
		List<String> yearMonth = generateYearMonthsBetween(start, end);
		return yearMonth;
	}

	public static YearMonth getPreviousYearMonth() {
		YearMonth currentYearMonth = YearMonth.from(LocalDate.now());
		return currentYearMonth.minusMonths(1);
	}

	public static List<String> getMonthDays(int year, int month) {
		YearMonth yearMonth = YearMonth.of(year, month);
		LocalDate firstDayOfMonth = yearMonth.atDay(1);
		int daysInMonth = yearMonth.lengthOfMonth();
		List<String> daysList = new ArrayList<>();
		for (int i = 0; i < daysInMonth; i++) {
			LocalDate date = firstDayOfMonth.plusDays(i);
			String formattedDate = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			daysList.add(formattedDate);
		}
		return daysList;
	}

	/**
	 * @描述 获取去年今日最大值
	 * @return
	 */
	public static String getLastYearTime() {
		LocalDate today = LocalDate.now();// 获取当前日期
		LocalDate lastYearToday = today.minusYears(1); // 获取去年同一日期
		return lastYearToday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd 59:59:59"));
	}

	public static String getLastMonthTime() {
		LocalDate today = LocalDate.now();// 获取当前日期
		LocalDate lastMonthToday = today.minusMonths(1);
		return lastMonthToday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd 59:59:59"));
	}

	public static Long getToadyStartTimestamp() {
		long startOfDayTimestamp = LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
		return startOfDayTimestamp / 1000;
	}

	public static String getBeforeByHourTime(int hour) {
		LocalDateTime hoursAgo = LocalDateTime.now().minus(Duration.ofHours(hour));
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		return hoursAgo.format(formatter);
	}

	public static Long getStartTimestampByYear(int year) {
		LocalDateTime startOfDay = LocalDateTime.of(year, 1, 1, 0, 0, 0);
		ZonedDateTime zonedStartOfDay = startOfDay.atZone(ZoneId.systemDefault());
		Instant startInstant = zonedStartOfDay.toInstant();
		return startInstant.getEpochSecond();
	}

	public static Long getEndTimestampByYear(int year) {
		LocalDateTime endOfDay = LocalDateTime.of(year, 12, 31, 23, 59, 59, 999_999_999);
		ZonedDateTime zonedEndOfDay = endOfDay.atZone(ZoneId.systemDefault());
		Instant endInstant = zonedEndOfDay.toInstant();
		return endInstant.getEpochSecond();
	}

	public static void main2(String[] args) {
		// 获取当前日期
        LocalDate today = LocalDate.now();
        
        // 获取去年的今天
        LocalDate lastYearToday = today.minusYears(1);
        
        // 设置为去年今天的开始时间（00:00:00）
        long startTimestamp = lastYearToday.atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
        long endTimestamp = lastYearToday.plusDays(1).atStartOfDay(ZoneId.systemDefault()).minusNanos(1).toEpochSecond();
        
        // 输出结果
        System.out.println("今年开始时间戳: " + StatTimeUtils.getStartTimestampByYear(2025));
        System.out.println("去年今日开始时间戳: " + StatTimeUtils.getEndTimestampByYear(2025));
        System.out.println("去年今日结束时间戳: " + endTimestamp);
        System.out.println("当前时间戳: " + System.currentTimeMillis()/1000);
        System.out.println("一个月前: " + TimeUtils.getBackTimeLong(1));
        

	}

}
