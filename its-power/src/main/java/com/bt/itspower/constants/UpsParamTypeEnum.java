package com.bt.itspower.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述：ups参数类型分类枚举
 *
 * <AUTHOR>
 * @since 2023-05-09 16:38
 */
public enum UpsParamTypeEnum {
    /**
     * 此处的几个分类是用于对应的分类对象构建
     */
    VOLTAGE(1, "电压"),
    ELECTRIC_RESISTANCE(2, "内阻"),
    TEMPERATURE(3, "温度"),
    GALVANIC(4, "电流");

    /**
     * 各个ups类型对应的具体的信号值以及描述，作用：基于设施查找，过滤其他的信号值
     */
    public static final Map<String, String> SIGNAL_MAP = new HashMap<>();
    public static final Map<String, String> VOLTAGE_MAP = new HashMap<>();
    public static final Map<String, String> VOLTAGE_ALARM_MAP = new HashMap<>();
    public static final Map<String, String> RESISTANCE_MAP = new HashMap<>();
    public static final Map<String, String> RESISTANCE_ALARM_MAP = new HashMap<>();
    public static final Map<String, String> TEMPERATURE_MAP = new HashMap<>();
    public static final Map<String, String> TEMPERATURE_ALARM_MAP = new HashMap<>();
    public static final Map<String, String> TEMPERATURE_ENV_MAP = new HashMap<>();
    public static final Map<String, String> TEMPERATURE_ENV_ALARM_MAP = new HashMap<>();
    public static final Map<String, String> GALVANIC_MAP = new HashMap<>();
    public static final Map<String, String> GALVANIC_ALARM_MAP = new HashMap<>();

    public static final Map<String,String> ALARM_MAP = new HashMap<>();

    // alarm相关的信号量查询的结果，其返回值如果大于0，则说明存在报警
    static {
        VOLTAGE_MAP.put("3514", "电池组电压");
        VOLTAGE_ALARM_MAP.put("4411", "组电压高");
        VOLTAGE_ALARM_MAP.put("4412", "组电压低");
        SIGNAL_MAP.putAll(VOLTAGE_MAP);
        SIGNAL_MAP.putAll(VOLTAGE_ALARM_MAP);
        ALARM_MAP.putAll(VOLTAGE_ALARM_MAP);

        RESISTANCE_MAP.put("3511", "单体内阻");
        RESISTANCE_ALARM_MAP.put("4429", "单体内阻高");
        RESISTANCE_ALARM_MAP.put("4430", "单体内阻低");
        SIGNAL_MAP.putAll(RESISTANCE_MAP);
        SIGNAL_MAP.putAll(RESISTANCE_ALARM_MAP);
        ALARM_MAP.putAll(RESISTANCE_ALARM_MAP);

        TEMPERATURE_MAP.put("3513", "电池温度");
        TEMPERATURE_ALARM_MAP.put("4431", "电池温度高");
        TEMPERATURE_ALARM_MAP.put("4432", "电池温度低");
        SIGNAL_MAP.putAll(TEMPERATURE_MAP);
        SIGNAL_MAP.putAll(TEMPERATURE_ALARM_MAP);
        ALARM_MAP.putAll(TEMPERATURE_ALARM_MAP);

        TEMPERATURE_ENV_MAP.put("3517", "环境温度");
        TEMPERATURE_ENV_ALARM_MAP.put("4415", "环境温度高");
        TEMPERATURE_ENV_ALARM_MAP.put("4416", "环境温度低");
        SIGNAL_MAP.putAll(TEMPERATURE_ENV_MAP);
        SIGNAL_MAP.putAll(TEMPERATURE_ENV_ALARM_MAP);
        ALARM_MAP.putAll(TEMPERATURE_ENV_ALARM_MAP);

        GALVANIC_MAP.put("3515", "充放电电流");
        GALVANIC_ALARM_MAP.put("4413", "充电电流大");
        GALVANIC_ALARM_MAP.put("4414", "放电电流小");
        SIGNAL_MAP.putAll(GALVANIC_MAP);
        SIGNAL_MAP.putAll(GALVANIC_ALARM_MAP);
        ALARM_MAP.putAll(GALVANIC_ALARM_MAP);

        // 电池组检测，返回值1是正常，其余为异常
        SIGNAL_MAP.put("9006", "UPS电池在线检测");
        // ups电池组剩余电量（百分比的值）
        SIGNAL_MAP.put("3519", "UPS电池组SOC");
        // (0-主回路市电，1-备回路市电，2-UPS供电，3-EPS供电，4-柴油发电机供电)
        SIGNAL_MAP.put("9002", "工作模式");
    }

    private int type;
    private String desc;

    UpsParamTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDescByType(Integer type) {
        if (type == null) {
            return "";
        }
        for (UpsParamTypeEnum typeEnum : UpsParamTypeEnum.values()) {
            if (type == typeEnum.getType()) {
                return typeEnum.getDesc();
            }
        }
        return "";
    }

    public static UpsParamTypeEnum getEnumByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (UpsParamTypeEnum typeEnum : UpsParamTypeEnum.values()) {
            if (type == typeEnum.getType()) {
                return typeEnum;
            }
        }
        return null;
    }
}
