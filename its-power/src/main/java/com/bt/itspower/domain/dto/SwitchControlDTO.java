package com.bt.itspower.domain.dto;

import java.io.Serializable;

/**
 * @Author: QinShiheng
 * @Date: 2022年11月02日17:16
 * @Description:
 **/
public class SwitchControlDTO implements Serializable {
    private String secret;
    private String facilityNo; // 设施编号
    private String deviceId; // 设备编号
    private String signalId;
    private Integer value;
    private String sourceId;//所属中心

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getFacilityNo() {
        return facilityNo;
    }

    public void setFacilityNo(String facilityNo) {
        this.facilityNo = facilityNo;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getSignalId() {
        return signalId;
    }

    public void setSignalId(String signalId) {
        this.signalId = signalId;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }
}
