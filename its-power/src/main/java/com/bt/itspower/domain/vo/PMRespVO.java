package com.bt.itspower.domain.vo;

import java.io.Serializable;
import java.util.List;

/**
 * 电力监控服务 -> 云控平台，GET_BY_ID、GET_BY_LOAD、PUSH_NOTIFY返回的信号量消息
 * @Author: QinShiheng
 * @Date: 2022年10月25日15:12
 * @Description:
 **/
public class PMRespVO implements Serializable {
    private String mqCid; // 随机数，MQ消息唯一编号，不能有负数
    private Integer cmdType; // 指令码，暂时只开放 GET_BY_ID(4) GET_BY_LOAD(3) SET_CONTROL(5) PUSH_NOTIFY(13)
    private String destUnit; // 目标电力监控单元，即隧道/设施的唯一编码
    private String retValue; // 错误码
    private Integer totalNum;
    private String sourceId; // 源站id
    private List<SignalItem> signalList;

    public String getMqCid() {
        return mqCid;
    }

    public void setMqCid(String mqCid) {
        this.mqCid = mqCid;
    }

    public Integer getCmdType() {
        return cmdType;
    }

    public void setCmdType(Integer cmdType) {
        this.cmdType = cmdType;
    }

    public String getDestUnit() {
        return destUnit;
    }

    public void setDestUnit(String destUnit) {
        this.destUnit = destUnit;
    }

    public String getRetValue() {
        return retValue;
    }

    public void setRetValue(String retValue) {
        this.retValue = retValue;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public List<SignalItem> getSignalList() {
        return signalList;
    }

    public void setSignalList(List<SignalItem> signalList) {
        this.signalList = signalList;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public class SignalItem implements Serializable{
        private String signalId;
        private Integer dataType;
        private String dataValue;
        private Integer isWarning;

        public String getSignalId() {
            return signalId;
        }

        public void setSignalId(String signalId) {
            this.signalId = signalId;
        }

        public Integer getDataType() {
            return dataType;
        }

        public void setDataType(Integer dataType) {
            this.dataType = dataType;
        }

        public String getDataValue() {
            return dataValue;
        }

        public void setDataValue(String dataValue) {
            this.dataValue = dataValue;
        }

        public Integer getIsWarning() {
            return isWarning;
        }

        public void setIsWarning(Integer isWarning) {
            this.isWarning = isWarning;
        }
    }
}
