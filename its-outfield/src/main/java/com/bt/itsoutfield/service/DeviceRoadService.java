package com.bt.itsoutfield.service;


import com.bt.itsoutfield.domain.dto.*;
import com.bt.itsoutfield.domain.entity.Event;
import com.bt.itsoutfield.domain.vo.*;
import com.bt.itsoutfield.mapper.*;
import com.bt.itsoutfield.utils.CmsUtils;
import com.bt.itsoutfield.utils.MyTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.TreeMap;

@Service("deviceRoadService")
public class DeviceRoadService {
    @Autowired
    private DeviceRoadMapper deviceRoadMapper;


    //获取设备列表
    public List<DeviceRoadVO> selectDeviceRoadList(DeviceRoadDTO roadDTO)
    {
        List<DeviceRoadVO> list = new ArrayList<>();
        DeviceCmsDTO cmsDTO = new DeviceCmsDTO();
        cmsDTO.setRoadNo(roadDTO.getRoadNo());
        cmsDTO.setFacilityNo(roadDTO.getFacilityNo());
        cmsDTO.setDirectionNo(roadDTO.getDirectionNo());
        cmsDTO.setStatus(roadDTO.getStatus());
        List<DeviceCmsVO> cmsList = deviceRoadMapper.selectCmsList(cmsDTO);
        for (DeviceCmsVO cms:cmsList) {
            list.add(convertDeviceCmsToDeviceRoad(cms));
        }

        DeviceVdDTO vdDTO = new DeviceVdDTO();
        vdDTO.setRoadNo(roadDTO.getRoadNo());
        vdDTO.setFacilityNo(roadDTO.getFacilityNo());
        vdDTO.setDirectionNo(roadDTO.getDirectionNo());
        vdDTO.setStatus(roadDTO.getStatus());
        List<DeviceVdVO> vdList = deviceRoadMapper.selectVdList(vdDTO);
        for (DeviceVdVO vd:vdList) {
                list.add(convertDeviceVdToDeviceRoad(vd));
        }

        DeviceWdDTO wdDTO = new DeviceWdDTO();
        wdDTO.setRoadNo(roadDTO.getRoadNo());
        wdDTO.setFacilityNo(roadDTO.getFacilityNo());
        wdDTO.setDirectionNo(roadDTO.getDirectionNo());
        wdDTO.setStatus(roadDTO.getStatus());
        List<DeviceWdVO> wdList = deviceRoadMapper.selectWdList(wdDTO);
        for (DeviceWdVO wd:wdList) {
            list.add(convertDeviceWdToDeviceRoad(wd));
        }
        return list;
    }

    //手动修改设备xy坐标
    public boolean updateDeviceLocation(DeviceRoadDTO roadDTO)
    {
        if("2".equals(roadDTO.getDeviceTypeNo())) {
            DeviceCmsDTO cmsDTO = new DeviceCmsDTO();
            cmsDTO.setDeviceId(roadDTO.getDeviceId());
            cmsDTO.setLayX(roadDTO.getLayX());
            cmsDTO.setLayY(roadDTO.getLayY());
            return deviceRoadMapper.updateCms(cmsDTO)>0;
        }
        else if("1".equals(roadDTO.getDeviceTypeNo()))
        {
            DeviceVdDTO vdDTO = new DeviceVdDTO();
            vdDTO.setDeviceId(roadDTO.getDeviceId());
            vdDTO.setLayX(roadDTO.getLayX());
            vdDTO.setLayY(roadDTO.getLayY());
            return deviceRoadMapper.updateVd(vdDTO)>0;
        }
        else if("7".equals(roadDTO.getDeviceTypeNo()))
        {
            DeviceWdDTO wdDTO = new DeviceWdDTO();
            wdDTO.setDeviceId(roadDTO.getDeviceId());
            wdDTO.setLayX(roadDTO.getLayX());
            wdDTO.setLayY(roadDTO.getLayY());
            return deviceRoadMapper.updateWd(wdDTO)>0;
        }
        else {
            return false;
        }
    }



    //获取情报板列表-仅更新情报板数据时使用
    private List<DeviceRoadVO> selectCmsList(DeviceRoadDTO roadDTO)
    {
        List<DeviceRoadVO> list = new ArrayList<>();
        DeviceCmsDTO cmsDTO = new DeviceCmsDTO();
        cmsDTO.setRoadNo(roadDTO.getRoadNo());
        cmsDTO.setFacilityNo(roadDTO.getFacilityNo());
        cmsDTO.setDirectionNo(roadDTO.getDirectionNo());
        cmsDTO.setStatus(roadDTO.getStatus());
        List<DeviceCmsVO> cmsList = deviceRoadMapper.selectCmsList(cmsDTO);
        for (DeviceCmsVO cms:cmsList) {
            list.add(convertDeviceCmsToDeviceRoad(cms));
        }
        return list;
    }

    public boolean pingDevice(DeviceRoadDTO roadDTO)
    {
        boolean flag = false;
        if("2".equals(roadDTO.getDeviceTypeNo()))
        {
            DeviceCmsDTO cmsDTO = new DeviceCmsDTO();
            cmsDTO.setDeviceId(roadDTO.getDeviceId());
            DeviceCmsVO cmsVO = deviceRoadMapper.selectCmsList(cmsDTO).get(0);
            flag = CmsUtils.ping(cmsVO.getIpAddress());
        }
        else if ("1".equals(roadDTO.getDeviceTypeNo()))
        {
            DeviceVdDTO vdDTO = new DeviceVdDTO();
            vdDTO.setDeviceId(roadDTO.getDeviceId());
            DeviceVdVO vdVO = deviceRoadMapper.selectVdList(vdDTO).get(0);
            flag = CmsUtils.ping(vdVO.getIpAddress());
        }
        else if ("7".equals(roadDTO.getDeviceTypeNo()))
        {
            DeviceWdDTO wdDTO = new DeviceWdDTO();
            wdDTO.setDeviceId(roadDTO.getDeviceId());
            DeviceWdVO wdVO = deviceRoadMapper.selectWdList(wdDTO).get(0);
            flag = CmsUtils.ping(wdVO.getIpAddress());
        }
        return flag;
    }

    private DeviceRoadVO convertDeviceCmsToDeviceRoad(DeviceCmsVO cmsVO)
    {
        DeviceRoadVO roadVO = new DeviceRoadVO();
        roadVO.setDeviceId(cmsVO.getDeviceId());
        roadVO.setDeviceName(cmsVO.getDeviceName());
        roadVO.setCmsType(cmsVO.getCmsType());
        roadVO.setRoadNo(cmsVO.getRoadNo());
        roadVO.setDirectionNo(cmsVO.getDirectionNo());
        roadVO.setFacilityName(cmsVO.getFacilityName());
        roadVO.setWidth(cmsVO.getWidth());
        roadVO.setHeight(cmsVO.getHeight());
        roadVO.setMilePost(cmsVO.getMilePost());
        roadVO.setStatus(cmsVO.getStatus());
        roadVO.setDirectionName(cmsVO.getDirectionName());
        roadVO.setProtocol(cmsVO.getProtocol());
        roadVO.setVersion(cmsVO.getVersion());
        roadVO.setIpAddress(cmsVO.getIpAddress());
        roadVO.setPort(cmsVO.getPort());
        roadVO.setLayX(cmsVO.getLayX());
        roadVO.setLayY(cmsVO.getLayY());
        roadVO.setDeviceTypeNo(cmsVO.getDeviceTypeNo());
        return roadVO;
    }

    private DeviceRoadVO convertDeviceVdToDeviceRoad(DeviceVdVO vdVO)
    {
        DeviceRoadVO roadVO = new DeviceRoadVO();
        roadVO.setDeviceId(vdVO.getDeviceId());
        roadVO.setDeviceName(vdVO.getDeviceName());
        roadVO.setVdType(vdVO.getVdType());
        roadVO.setRoadNo(vdVO.getRoadNo());
        roadVO.setDirectionNo(vdVO.getDirectionNo());
        roadVO.setFacilityName(vdVO.getFacilityName());
        roadVO.setWidth(vdVO.getWidth());
        roadVO.setHeight(vdVO.getHeight());
        roadVO.setMilePost(vdVO.getMilePost());
        roadVO.setStatus(vdVO.getStatus());
        roadVO.setDirectionName(vdVO.getDirectionName());
        roadVO.setProtocol(vdVO.getProtocol());
        roadVO.setVersion(vdVO.getVersion());
        roadVO.setIpAddress(vdVO.getIpAddress());
        roadVO.setPort(vdVO.getPort());
        roadVO.setLayX(vdVO.getLayX());
        roadVO.setLayY(vdVO.getLayY());
        roadVO.setDeviceTypeNo(vdVO.getDeviceTypeNo());
        return roadVO;
    }

    private DeviceRoadVO convertDeviceWdToDeviceRoad(DeviceWdVO wdVO)
    {
        DeviceRoadVO roadVO = new DeviceRoadVO();
        roadVO.setDeviceId(wdVO.getDeviceId());
        roadVO.setDeviceName(wdVO.getDeviceName());
        roadVO.setWdType(wdVO.getWdType());
        roadVO.setRoadNo(wdVO.getRoadNo());
        roadVO.setDirectionNo(wdVO.getDirectionNo());
        roadVO.setFacilityName(wdVO.getFacilityName());
        roadVO.setWidth(wdVO.getWidth());
        roadVO.setHeight(wdVO.getHeight());
        roadVO.setMilePost(wdVO.getMilePost());
        roadVO.setStatus(wdVO.getStatus());
        roadVO.setDirectionName(wdVO.getDirectionName());
        roadVO.setProtocol(wdVO.getProtocol());
        roadVO.setVersion(wdVO.getVersion());
        roadVO.setIpAddress(wdVO.getIpAddress());
        roadVO.setPort(wdVO.getPort());
        roadVO.setLayX(wdVO.getLayX());
        roadVO.setLayY(wdVO.getLayY());
        roadVO.setDeviceTypeNo(wdVO.getDeviceTypeNo());
        return roadVO;
    }

    public List<DeviceVdVO> selectVdList (DeviceVdDTO deviceVdDTO)
    {
        return deviceRoadMapper.selectVdList(deviceVdDTO);
    }

    public List<DeviceCmsVO> selectCmsList(DeviceCmsDTO cms)
    {
        List<DeviceCmsVO> list = null;
        if(cms.getNoAuth()!=null)
        {
            list =  deviceRoadMapper.selectCmsInner(cms);
        }
        else {
            list = deviceRoadMapper.selectCmsList(cms);
        }
        //如果没有位置ID，手动做过滤
        for (DeviceCmsVO c:list) {
            if(c.getDeviceName().contains("站前"))
            {
                c.setRoadLine(2);
            }
            if(c.getPositionId()==null)
            {
                if(c.getDeviceName().contains("站前"))
                    c.setPositionId(3);
                else if(c.getDeviceName().contains("站"))
                    c.setPositionId(4);
                else if(c.getDeviceName().contains("服务区")) {
                    if(c.getDeviceName().contains("信息"))
                        c.setPositionId(1);
                    else
                        c.setPositionId(2);
                }
                else if(c.getDeviceName().contains("隧道"))
                {
                    if(c.getDeviceName().contains("内"))
                        c.setPositionId(5);
                    else
                        c.setPositionId(6);
                }
            }
        }
        return list;
    }

    public List<DeviceCmsVO> selectCmsInner(DeviceCmsDTO cms)
    {

        return deviceRoadMapper.selectCmsInner(cms);

    }

    public List<DeviceWdVO> selectWdList (DeviceWdDTO deviceWdDTO)
    {
        return deviceRoadMapper.selectWdList(deviceWdDTO);
    }

    //获取事故点后方的设备
    public DeviceVdVO getEventVd(Event dto)
    {
        //获取上下行
        Event event = deviceRoadMapper.selectEventDirection(dto);
        //桩号转换为数值
        String milePost = dto.getMilePost();
        milePost = milePost.replace("K", "");
        milePost = milePost.replace("+", "");
        Integer mp = Integer.parseInt(milePost);
        dto.setMpValue(mp);
        dto.setDeviceTypeNo("1");
        //根据上下行查找设备
        Event device = deviceRoadMapper.selectEventDevice(dto);
        return null;
    }
}
