package com.bt.itsoutfield.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bt.itsoutfield.domain.entity.CmsPlan;

import java.util.List;

import com.bt.itsoutfield.mapper.CmsPlanMapper;

@Service("cmsPlanService")
public class CmsPlanService {
    //情报板预案服务

    @Autowired
    private CmsPlanMapper cmsPlanMapper;
    @Autowired
    private AttributionService attributionService;

    //初始化默认加载预案列表
    public List<CmsPlan> select(CmsPlan cmsPlan)
    {
        return cmsPlanMapper.select(cmsPlan);
    }

    //新增交通策略
    public boolean add(CmsPlan plan)
    {
        return cmsPlanMapper.insert(plan)>0;
    }

    //更新交通策略
    public boolean update(CmsPlan plan)
    {
        return cmsPlanMapper.update(plan)>0;
    }

    //删除交通策略
    public boolean delete(CmsPlan plan)
    {
        return cmsPlanMapper.delete(plan)>0;
    }

}
