package com.bt.itsoutfield.feign;

import com.bt.itscore.auth.Login;
import com.bt.itscore.domain.dto.CommonParamDTO;
import com.bt.itsoutfield.domain.dto.AlarmDTO;
import com.bt.itsoutfield.feign.vo.GantryFlowDetailVO;
import com.bt.itsoutfield.feign.vo.GantryFlowVO;
import com.bt.itsoutfield.feign.vo.TreeVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(name = "its-device")
public interface ItsDeviceFeignClient {
	/**
	 * 查询所有设施列表
	 * @param roadNo
	 * @param facilityTypeNo
	 * @return
	 */
	@PostMapping("/facility/selectTree")
	public List<TreeVo> selectList(@RequestParam("roadNo") Integer roadNo, @RequestParam("facilityTypeNo") String facilityTypeNo);
}
