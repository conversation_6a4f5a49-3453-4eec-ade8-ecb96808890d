package com.bt.itsoutfield.feign;

import com.bt.itscore.domain.vo.UserSimpleVO;
import com.bt.itsoutfield.feign.dto.EventDistributeVO;
import com.bt.itsoutfield.feign.dto.RoleEventDTO;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import javax.servlet.http.HttpServletRequest;


@FeignClient(name = "its-user")
public interface ItsUserFeignClient {
	@PostMapping("/user/selectByUserId")
	public UserSimpleVO selectByUserId(String userId) ;

	@PostMapping("/user/selectPersonalInfo")
	public UserSimpleVO selectPersonalInfo(HttpServletRequest request);

	@PostMapping("/dataPermissions/selectEventByRoleId")
	public EventDistributeVO selectEventByRoleId(RoleEventDTO roleEventDTO);
}
