package com.bt.itsoutfield.feign;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.bt.itscore.domain.dto.CommonParamDTO;
import com.bt.itsoutfield.domain.dto.AlarmDTO;
import com.bt.itsoutfield.feign.vo.GantryFlowDetailVO;
import com.bt.itsoutfield.feign.vo.GantryFlowVO;


@FeignClient(name = "its-road")
public interface ItsRoadFeignClient {
	//根据路段和关键字获取门架车流量信息
	@PostMapping("/carflow/mapGantryAll")
	public List<GantryFlowVO> mapGantryAll(@RequestBody CommonParamDTO commonParamDTO);
	
	//根据查询条件,获取门架详细信息列表
	@PostMapping("/carflow/mapGantryDetail")
	public GantryFlowDetailVO mapGantryDetail(@RequestBody CommonParamDTO commonParamDTO);

	//报警入库
	@PostMapping(value = "/alarm/add")
	public Object alarmAdd(@RequestBody AlarmDTO dto);
}
