<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsoutfield.mapper.CmsOperationRecordMapper">
  <resultMap id="BaseResultMap" type="com.bt.itsoutfield.domain.dto.CmsOperationRecord">
    <id column="id" property="id" />
    <result column="cms_id" property="cmsId" />
    <result column="type" property="type" />
    <result column="detail" property="detail" />
    <result column="user_id" property="userId" />
    <result column="user_name" property="userName" />
    <result column="time" property="time" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, cms_id, `type`, detail, user_id, user_name, `time`
  </sql>

  <select id="select" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cms_operation_record
    where id = #{id}
  </select>

  <delete id="delete" parameterType="java.lang.Integer">
    delete from cms_operation_record
    where id = #{id}
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.bt.itsoutfield.domain.dto.CmsOperationRecord" useGeneratedKeys="true">
    insert into cms_operation_record (cms_id, `type`, detail, user_id, user_name, `time`)
    values (#{cmsId}, #{type}, #{detail}, #{userId}, #{userName}, #{time})
  </insert>


  <update id="update" parameterType="com.bt.itsoutfield.domain.dto.CmsOperationRecord">
    update cms_operation_record
    <set>
      <if test="cmsId != null">
        cms_id = #{cmsId},
      </if>
      <if test="type != null">
        `type` = #{type},
      </if>
      <if test="detail != null">
        detail = #{detail},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="userName != null">
        user_name = #{userName},
      </if>
      <if test="time != null">
        `time` = #{time},
      </if>
    </set>
    where id = #{id}
  </update>

  <update id="updateBatch" parameterType="java.util.List">
    update cms_operation_record
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="cms_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.cmsId}
        </foreach>
      </trim>
      <trim prefix="`type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.type}
        </foreach>
      </trim>
      <trim prefix="detail = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.detail}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.userId}
        </foreach>
      </trim>
      <trim prefix="user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.userName}
        </foreach>
      </trim>
      <trim prefix="`time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.time}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into cms_operation_record
    (cms_id, `type`, detail, user_id, user_name, `time`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.cmsId}, #{item.type}, #{item.detail}, #{item.userId}, #{item.userName}, #{item.time})
    </foreach>
  </insert>

</mapper>