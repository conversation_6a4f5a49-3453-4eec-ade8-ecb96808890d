package com.bt.itsoutfield.mapper;

import com.bt.itsoutfield.domain.entity.CmsPlan;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CmsPlanMapper {

    List<CmsPlan> select(CmsPlan cmsPlan);

    int insert(CmsPlan cmsPlan);

    int update(CmsPlan cmsPlan);

    int delete(CmsPlan cmsPlan);

    int batchInsert(@Param("list") List<CmsPlan> list);
}