<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsoutfield.mapper.TvsFlowMapper">
	<resultMap type="com.bt.itsoutfield.domain.vo.TvsFlowVO" id="tvsFlowMap">
		<result property="id" column="id"/>
		<result property="deviceId" column="device_id"></result>
		<result property="vdName" column="vd_name" ></result>
		<result property="vdDirection" column="vd_direction"></result>
		<result property="statTitle"  column="stat_title"></result>
		<result property="statTitleId" column="stat_title_id"></result>
		<result property="zxCar"  column="zx_car"></result>
		<result property="zxCarSpeed" column="zx_car_speed"></result>
		<result property="xTruck" column="x_truck"></result>
		<result property="xTruckSpeed" column="x_truck_speed"></result>
		<result property="dCar"  column="d_car"></result>
		<result property="dCarSpeed"  column="d_car_speed"></result>
		<result property="zTruck"  column="z_truck"></result>
		<result property="zTruckSpeed"  column="z_truck_speed"></result>
		<result property="dTruck"  column="d_truck"></result>
		<result property="dTruckSpeed"  column="d_truck_speed"></result>
		<result property="tdTruck"  column="td_truck"></result>
		<result property="tdTruckSpeed"  column="td_truck_speed"></result>
		<result property="jzxCar"  column="jzx_car"></result>
		<result property="jzxCarSpeed"  column="jzx_car_speed"></result>
		<result property="tljCar"  column="tlj_car"></result>
		<result property="tljCarSpeed"  column="tlj_car_speed"></result>
		<result property="motoCar"  column="moto_car"></result>
		<result property="motoCarSpeed"  column="moto_car_speed"></result>
		<result property="writeDate"  column="write_date"></result>
		<result property="vdType"  column="vd_type"></result>
		<result property="vdModel"  column="vd_model"></result>
		<result property="carEqu"  column="carEqu"></result>
		<result property="avgSpeed"  column="avgSpeed"></result>
		<result property="avgCrowd"  column="avgCrowd"></result>
		<result property="sumCar"  column="sumCar"></result>
		<result property="truckEqu"  column="truckEqu"></result>
		<result property="laneNum"  column="lane_num"></result>
	</resultMap>

	<select id="getTypeFlow" resultMap="tvsFlowMap" parameterType="com.bt.itsoutfield.domain.dto.VdFlowDTO">
		select vv.*,dv.lane_num from(
		SELECT vdc.device_id,
		min(vdc.vd_name) as vd_name,
		vdc.vd_direction,
		sum(vdc.zx_car) zx_car,
		sum(vdc.d_car) d_car,
		sum(vdc.x_truck) x_truck,
		sum(vdc.z_truck) z_truck,
		sum(vdc.d_truck) d_truck,
		sum(vdc.td_truck) td_truck,
		SUM(vdc.jzx_car) jzx_car,
		SUM(vdc.moto_car) moto_car,
		sum(vdc.tlj_car) tlj_car,
		sum(zx_car+d_car+x_truck+z_truck+d_truck+td_truck+jzx_car+moto_car+tlj_car) as sumCar,
		any_value(DATE_FORMAT(vdc.write_date,#{dateFormat})) as write_date
		FROM (
		select * from vd_traffic_dayhistory v1
		<where>
			<if test="deviceId != null and deviceId != ''">AND v1.device_id=#{deviceId}</if>
			<if test="vdDirection != null and vdDirection != ''">AND v1.vd_direction=#{vdDirection}</if>
			<if test="createDateStart != null and createDateStart != '' and createDateEnd != null and createDateEnd != ''">
				AND v1.timestamp >= unix_timestamp(#{createDateStart}) AND v1.timestamp &lt;=
				unix_timestamp(#{createDateEnd})</if>
		</where>
		union all
		select * from vd_traffic_today v2
		<where>
			<if test="deviceId != null and deviceId != ''">AND v2.device_id=#{deviceId}</if>
			<if test="vdDirection != null and vdDirection != ''">AND v2.vd_direction=#{vdDirection}</if>
			<if test="createDateStart != null and createDateStart != '' and createDateEnd != null and createDateEnd != ''">
				AND v2.timestamp >= unix_timestamp(#{createDateStart}) AND v2.timestamp &lt;=
				unix_timestamp(#{createDateEnd})
				and TO_DAYS(v2.write_date) =TO_DAYS(now()) </if>
		</where>
		)
		vdc
		GROUP BY vdc.device_id,vdc.vd_direction,DATE_FORMAT(vdc.write_date,#{dateFormat})
		) vv
		LEFT JOIN device d ON vv.device_id=d.device_id
		LEFT JOIN device_vd dv ON vv.device_id=dv.device_id
		LEFT JOIN facility f on d.facility_no = f.facility_no
		<where>
	   		<if test="roadNo != null and roadNo != ''">f.road_no=#{roadNo}</if>
            <if test="vdType != null and vdType != '' and (vdType == 7 or vdType == 8)">AND (dv.vd_TYPE=#{vdType})</if>
            <if test="vdType != null and vdType != '' and (vdType != 7 and vdType != 8)">AND (dv.vd_model=#{vdModel} and dv.vd_TYPE not in (7,8))</if>
            <if test="facilityNo != null and facilityNo != ''">AND d.facility_no = #{facilityNo}</if>
            AND d.facility_no IN (Facility-Permissions-Check)
	   		and d.use = 1 AND d.del_status = 0
	   	</where>
		ORDER BY CONCAT(vv.device_id,any_value(vv.write_date),vv.vd_direction)
	</select>

	<select id="getTodayType" resultMap="tvsFlowMap" parameterType="com.bt.itsoutfield.domain.dto.VdFlowDTO">
		SELECT vdc.device_id,
		       min(vdc.vd_name) as vd_name,
		       min(vdc.vd_direction) as vd_direction,
		       sum(vdc.zx_car) zx_car,
		       sum(vdc.d_car) d_car,
		       sum(vdc.x_truck) x_truck,
		       sum(vdc.z_truck) z_truck,
		       sum(vdc.d_truck) d_truck,
		       sum(vdc.td_truck) td_truck,
		       SUM(vdc.jzx_car) jzx_car,
		       SUM(vdc.moto_car) moto_car,
		       sum(vdc.tlj_car) tlj_car,
		round(sum(zx_car_speed*zx_car)/sum(vdc.zx_car),1) zx_car_speed,
		round(sum(d_car_speed*d_car)/sum(vdc.d_car),1) d_car_speed,
		round(sum(x_truck_speed*x_truck)/sum(vdc.x_truck),1) x_truck_speed,
		round(sum(z_truck_speed*z_truck)/sum(vdc.z_truck),1) z_truck_speed,
		round(sum(d_truck_speed*d_truck)/sum(vdc.d_truck),1) d_truck_speed,
		round(sum(td_truck_speed*td_truck)/sum(vdc.td_truck),1) td_truck_speed,
		round(sum(jzx_car_speed*jzx_car)/sum(vdc.jzx_car),1) jzx_car_speed,
		round(sum(moto_car_speed*moto_car)/sum(vdc.moto_car),1) moto_car_speed,
		round(sum(tlj_car_speed*tlj_car)/sum(vdc.tlj_car),1) tlj_car_speed,
		any_value(DATE_FORMAT(vdc.write_date,#{dateFormat})) write_date FROM (
		select * from
		vd_traffic_today v
		<where>
			<if test="deviceId != null and deviceId != ''">AND v.device_id=#{deviceId}</if>
			<if test="vdDirection != null and vdDirection != ''">AND v.vd_direction=#{vdDirection}</if>
			<if test="createDateStart == null ">
				and TO_DAYS(v.write_date) = TO_DAYS(now())</if>
			<if test="createDateStart != null and createDateEnd != null ">
				AND v.timestamp >= unix_timestamp(#{createDateStart}) AND v.timestamp &lt;= unix_timestamp(#{createDateEnd})</if>
		</where>
		) vdc
		LEFT JOIN device_vd dv ON vdc.device_id=dv.device_id
		LEFT JOIN device d ON vdc.device_id=d.device_id
		LEFT JOIN facility f on d.facility_no = f.facility_no
		<where>
			<if test="roadNo != null and roadNo != ''">f.road_no=#{roadNo}</if>
			<if test="vdType != null and vdType != '' and (vdType == 7 or vdType == 8)">AND (dv.vd_TYPE=#{vdType})</if>
			<if test="vdType != null and vdType != '' and (vdType != 7 and vdType != 8)">AND (dv.vd_model=#{vdModel} and dv.vd_TYPE not in (7,8))</if>
			<if test="facilityNo != null and facilityNo != ''">AND d.facility_no = #{facilityNo}
			</if>
			AND d.facility_no IN (Facility-Permissions-Check)
			and d.use = 1 AND d.del_status = 0
		</where>
		GROUP BY vdc.device_id,
		<if test="vdDirection != null and vdDirection != ''">vdc.vd_direction,</if>
		  DATE_FORMAT(vdc.write_date,#{dateFormat})
		ORDER BY CONCAT(vdc.device_id,any_value(vdc.write_date),any_value(vdc.vd_direction))
	</select>

<!--	<select id="getTypeFlow" resultMap="tvsFlowMap" parameterType="com.bt.itsoutfield.domain.dto.VdFlowDTO">-->
<!--		SELECT vdc.device_id,-->
<!--		min(vdc.vd_name) as vd_name,-->
<!--		vdc.vd_direction,-->
<!--		sum(vdc.zx_car) zx_car,-->
<!--		sum(vdc.d_car) d_car,-->
<!--		sum(vdc.x_truck) x_truck,-->
<!--		sum(vdc.z_truck) z_truck,-->
<!--		sum(vdc.d_truck) d_truck,-->
<!--		sum(vdc.td_truck) td_truck,-->
<!--		SUM(vdc.jzx_car) jzx_car,-->
<!--		SUM(vdc.moto_car) moto_car,-->
<!--		sum(vdc.tlj_car) tlj_car,-->
<!--		(sum(zx_car)+sum(d_car)*1.5+sum(x_truck)+sum(z_truck)*1.5+sum(d_truck)*3+sum(td_truck)*4+sum(jzx_car)*4) carEqu,-->
<!--		(sum(zx_car)+sum(d_car)*1.5+sum(x_truck)+sum(z_truck)*1.5+sum(d_truck)*3+sum(td_truck)*4+sum(jzx_car)*4+sum(tlj_car)*4+sum(moto_car)) truckEqu,-->
<!--		round((sum(zx_car_speed*zx_car)+sum(d_car_speed*d_car)+sum(x_truck_speed*x_truck)+sum(z_truck_speed*z_truck)+sum(d_truck_speed*d_truck)+sum(td_truck_speed*td_truck)+sum(jzx_car_speed*jzx_car)+sum(tlj_car_speed*tlj_car)+sum(moto_car_speed*moto_car))/-->
<!--		(sum(zx_car)+sum(d_car)+sum(x_truck)+sum(z_truck)+sum(d_truck)+sum(td_truck)+sum(jzx_car)+sum(tlj_car)+sum(moto_car)),1) avgSpeed,-->
<!--		round((sum(zx_car)+sum(d_car)*1.5+sum(x_truck)+sum(z_truck)*1.5+sum(d_truck)*3+sum(td_truck)*4+sum(jzx_car)*4+sum(tlj_car)*4+sum(moto_car))/(CASE dv.lane_num WHEN 8 THEN 130000 WHEN 6 THEN 100000 ELSE 65000 END),6) avgCrowd,-->
<!--		sum(zx_car+d_car+x_truck+z_truck+d_truck+td_truck+jzx_car+moto_car+tlj_car) as sumCar,-->
<!--		any_value(DATE_FORMAT(vdc.write_date,#{dateFormat})) as write_date-->
<!--		FROM (-->
<!--		select * from vd_traffic_hourhistory v1-->
<!--		<where>-->
<!--			<if test="deviceId != null and deviceId != ''">AND v1.device_id=#{deviceId}</if>-->
<!--			<if test="vdDirection != null and vdDirection != ''">AND v1.vd_direction=#{vdDirection}</if>-->
<!--			<if test="createDateStart != null and createDateStart != '' and createDateEnd != null and createDateEnd != ''">-->
<!--				AND v1.timestamp >= unix_timestamp(#{createDateStart}) AND v1.timestamp &lt;= unix_timestamp(#{createDateEnd})</if>-->
<!--		</where>-->
<!--		union all-->
<!--		select * from vd_traffic_hour v2-->
<!--		<where>-->
<!--			<if test="deviceId != null and deviceId != ''">AND v2.device_id=#{deviceId}</if>-->
<!--			<if test="vdDirection != null and vdDirection != ''">AND v2.vd_direction=#{vdDirection}</if>-->
<!--			<if test="createDateStart != null and createDateStart != '' and createDateEnd != null and createDateEnd != ''">-->
<!--				AND v2.timestamp >= unix_timestamp(#{createDateStart}) AND v2.timestamp &lt;= unix_timestamp(#{createDateEnd})</if>-->
<!--		</where>-->
<!--		) vdc-->
<!--		LEFT JOIN device_vd dv ON vdc.device_id=dv.device_id-->
<!--		LEFT JOIN device d ON vdc.device_id=d.device_id-->
<!--		LEFT JOIN facility f on d.facility_no = f.facility_no-->
<!--		<where>-->
<!--			<if test="roadNo != null and roadNo != ''">f.road_no=#{roadNo}</if>-->
<!--			<if test="vdType != null and vdType != '' and (vdType == 7 or vdType == 8)">AND (dv.vd_TYPE=#{vdType})</if>-->
<!--			<if test="vdType != null and vdType != '' and (vdType != 7 and vdType != 8)">AND (dv.vd_model=#{vdModel} and dv.vd_TYPE not in (7,8))</if>-->
<!--			<if test="facilityNo != null and facilityNo != ''">AND d.facility_no = #{facilityNo}-->
<!--				AND d.facility_no IN (Facility-Permissions-Check)-->
<!--			</if>-->
<!--			and d.use = 1 AND d.del_status = 0-->
<!--		</where>-->
<!--		GROUP BY vdc.device_id,vdc.vd_direction,DATE_FORMAT(vdc.write_date,#{dateFormat})-->
<!--		ORDER BY CONCAT(vdc.device_id,any_value(vdc.write_date),vdc.vd_direction)-->
<!--	</select>-->



</mapper>