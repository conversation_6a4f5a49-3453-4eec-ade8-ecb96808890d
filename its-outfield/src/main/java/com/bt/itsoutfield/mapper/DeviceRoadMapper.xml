<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsoutfield.mapper.DeviceRoadMapper">

    <resultMap type="com.bt.itsoutfield.domain.vo.DeviceCmsVO" id="deviceCmsMap">
        <result property="facilityNo" column="facility_no"/>
        <result property="directionNo" column="direction_no"/>
        <result property="deviceName" column="device_name"/>
        <result property="roadName" column="road_name"/>
        <result property="roadAlias" column="road_alias"/>
        <result property="deviceTypeNo" column="device_type_no"/>
        <result property="milePost" column="mile_post"/>
        <result property="mpValue" column="mp_value"/>
        <result property="use" column="use"/>
        <result property="status" column="status"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="roadNo" column="road_no"/>
        <result property="computeSwitch" column="compute_switch"/>
        <result property="directionName" column="direction_name"/>
        <result property="facilityName" column="facility_name"/>
        <result property="deviceId" column="device_id"/>
        <result property="width" column="width"/>
        <result property="height" column="height"/>
        <result property="cmsType" column="cms_type"/>
        <result property="protocol" column="protocol"/>
        <result property="version" column="version"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="port" column="port"/>
        <result property="comPort" column="com_port"/>
        <result property="protocolFlag" column="protocol_flag"/>
        <result property="isScl2008" column="is_scl2008"/>
        <result property="positionId" column="position_id"/>
        <result property="layY" column="lay_y"/>
        <result property="layX" column="lay_x"/>
        <result property="sourceId" column="source_id"/>
        <result property="deviceSubtypeName" column="device_subtype_name"/>
        <result property="facilityTypeNo" column="facility_type_no"/>
        <result property="facilityTypeName" column="facility_type_name"/>
        <result property="roadLine" column="road_line"/>
        <result property="positionId" column="position_id"/>
        <result property="distance" column="distance"/>
    </resultMap>

    <resultMap type="com.bt.itsoutfield.domain.vo.DeviceVdVO" id="deviceVdMap">
        <result property="facilityNo" column="facility_no"/>
        <result property="directionNo" column="direction_no"/>
        <result property="deviceName" column="device_name"/>
        <result property="deviceTypeNo" column="device_type_no"/>
        <result property="milePost" column="mile_post"/>
        <result property="mpValue" column="mp_value"/>
        <result property="use" column="use"/>
        <result property="status" column="status"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="orgId" column="org_id"/>
        <result property="roadNo" column="road_no"/>
        <result property="computeSwitch" column="compute_switch"/>
        <result property="directionName" column="direction_name"/>
        <result property="facilityName" column="facility_name"/>
        <result property="deviceId" column="device_id"/>
        <result property="vdType" column="vd_type"/>
        <result property="protocol" column="protocol"/>
        <result property="vdModel" column="vd_model"/>
        <result property="version" column="version"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="port" column="port"/>
        <result property="comPort" column="com_port"/>
        <result property="protocolFlag" column="protocol_flag"/>
        <result property="laneNum" column="lane_num"/>
        <result property="height" column="height"/>
        <result property="width" column="width"/>
        <result property="layY" column="lay_y"/>
        <result property="layX" column="lay_x"/>
        <result property="roadName" column="road_name"/>
    </resultMap>

    <resultMap type="com.bt.itsoutfield.domain.vo.DeviceWdVO" id="deviceWdMap">
        <result property="facilityNo" column="facility_no"/>
        <result property="directionNo" column="direction_no"/>
        <result property="deviceName" column="device_name"/>
        <result property="deviceTypeNo" column="device_type_no"/>
        <result property="milePost" column="mile_post"/>
        <result property="mpValue" column="mp_value"/>
        <result property="use" column="use"/>
        <result property="status" column="status"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="roadNo" column="road_no"/>
        <result property="computeSwitch" column="compute_switch"/>
        <result property="directionName" column="direction_name"/>
        <result property="facilityName" column="facility_name"/>
        <result property="deviceId" column="device_id"/>
        <result property="protocol" column="protocol"/>
        <result property="version" column="version"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="port" column="port"/>
        <result property="comPort" column="com_port"/>
        <result property="protocolFlag" column="protocol_flag"/>
        <result property="height" column="height"/>
        <result property="width" column="width"/>
        <result property="layY" column="lay_y"/>
        <result property="layX" column="lay_x"/>
        <result property="cameraId" column="camera_id"/>
        <result property="kindId" column="kind_id"/>
        <result property="wdType" column="wd_type"/>
        <result property="roadName" column="road_name"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
    </resultMap>

    <resultMap type="com.bt.itsoutfield.domain.vo.DeviceVO" id="deviceMap">
        <result property="facilityNo" column="facility_no"/>
        <result property="directionNo" column="direction_no"/>
        <result property="deviceName" column="device_name"/>
        <result property="deviceTypeNo" column="device_type_no"/>
        <result property="milePost" column="mile_post"/>
        <result property="mpValue" column="mp_value"/>
        <result property="use" column="use"/>
        <result property="status" column="status"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="roadNo" column="road_no"/>
        <result property="roadName" column="road_name"/>
        <result property="directionName" column="direction_name"/>
        <result property="facilityName" column="facility_name"/>
        <result property="deviceId" column="device_id"/>
    </resultMap>

    <select id="selectCmsList" resultMap="deviceCmsMap" parameterType="com.bt.itsoutfield.domain.dto.DeviceCmsDTO">
        select f.facility_name,f.road_no,f.facility_type_no,dd.*,r.road_alias,r.road_name,
        e.direction_name,e.road_line,dt.device_subtype_name,ft.facility_type_name from
        ( select c.*,d.status,d.device_name,d.mile_post,d.source_id,
        d.lng,d.lat,d.facility_no,d.device_type_no,d.direction_no from device d, device_cms c
        <if test="roadNo != null">,facility f2 </if>
        where d.device_type_no=2 and d.device_id=c.device_id and d.use=1 AND d.del_status = 0
        <if test="deviceName != null &amp;&amp; deviceName != '' "> AND (d.device_name like CONCAT('%', #{deviceName}, '%')
            or d.MILE_POST like CONCAT('%', #{deviceName}, '%')) </if>
        <if test="deviceId != null"> AND d.device_id =#{deviceId}</if>
        <if test="facilityNo != null"> AND d.facility_no =#{facilityNo}</if>
        <if test="use != null &amp;&amp; use>=0 "> AND d.use =#{use}</if>
        <if test="status != null &amp;&amp; status>=0 "> AND d.status =#{status}</if>
        <if test="cmsType != null &amp;&amp; cmsType >=0 "> AND c.cms_type =#{cmsType}</if>
        <if test="width != null &amp;&amp; width>=0 "> AND c.width =#{width}</if>
        <if test="height != null &amp;&amp; height>=0 "> AND c.height =#{height}</if>
        <if test="roadNo != null &amp;&amp; roadNo > 0 "> and f2.road_no =#{roadNo} and f2.facility_no =d.facility_no </if>
        <if test="directionNo != null"> and d.direction_no =#{directionNo}</if>
        <if test="idList != null &amp;&amp; idList.size()>0">
            and d.device_id in
            <foreach collection="idList" item="deviceId" index="index" open="(" close=")" separator=",">
                concat("",#{deviceId},"")
            </foreach>
        </if>
        <if test="facilities != null &amp;&amp; facilities.size()>0">
            AND d.facility_no IN
            <foreach collection="facilities" item="facilityNo" index="index" open="(" close=")" separator=",">
                concat("",#{facilityNo},"")
            </foreach>
        </if>
        ) dd
        left join direction e on dd.direction_no=e.direction_no
        left join device_subtype_dict dt on dd.cms_type=dt.device_subtype_no and dd.device_type_no=dt.device_type_no
        left join facility f on dd.facility_no=f.facility_no
        left join facility_type ft on f.facility_type_no=ft.facility_type_no
        left join road r on f.road_no=r.road_no
        WHERE f.road_no is not null
        AND dd.mile_post is not NULL
        AND dd.device_name is not NULL
        and dd.facility_no IN (Facility-Permissions-Check)
        order by e.road_line,dd.mile_post asc
    </select>

    <select id="selectVdList" resultMap="deviceVdMap" parameterType="com.bt.itsoutfield.domain.dto.DeviceVdDTO">
        select f.facility_name,f.facility_type_no,dd.*,r.road_name,
        e.direction_name,dt.device_subtype_name from
        ( select d.device_name,d.mile_post,d.lng,d.lat,d.facility_no,
        d.device_type_no,d.direction_no,d.status,d.use,d.mp_value,c.* from device d,device_vd c
        <if test="roadNo != null &amp;&amp; roadNo > 0 ">,facility f2 </if>
        where d.device_type_no=1 and d.device_id=c.device_id and d.use=1 AND d.del_status = 0
        <if test="deviceId != null and deviceId != ''">AND d.device_id=#{deviceId}</if>
        <if test="deviceName != null &amp;&amp; deviceName != '' "> AND (d.device_name like CONCAT('%', #{deviceName}, '%') or d.MILE_POST like CONCAT('%', #{deviceName}, '%')) </if>
        <if test="facilityNo != null &amp;&amp; facilityNo != '' "> AND d.facility_no =#{facilityNo}</if>
        <if test="use != null &amp;&amp; use>=0 "> AND d.use =#{use}</if>
        <if test="roadNo != null &amp;&amp; roadNo > 0 "> and f2.road_no =#{roadNo} and f2.facility_no =d.facility_no </if>
        <if test="directionNo != null &amp;&amp; directionNo != '' "> AND d.direction_no =#{directionNo}</if>
        ) dd
        left join direction e on dd.direction_no=e.direction_no
        left join device_subtype_dict dt on dd.vd_type=dt.device_subtype_no and dd.device_type_no=dt.device_type_no
        left join facility f on dd.facility_no=f.facility_no
        left join road r on r.road_no = f.road_no
        where dd.facility_no IN (Facility-Permissions-Check)
        <if test="facilityTypeNo != null &amp;&amp; facilityTypeNo>0 "> and f.facility_type_no=#{facilityTypeNo} </if>
        order by dd.device_id desc
    </select>



    <select id="selectWdList" resultMap="deviceWdMap" parameterType="com.bt.itsoutfield.domain.dto.DeviceWdDTO">
        select f.facility_name,f.facility_type_no,dd.*,e.direction_name,r.road_name,
        dt.device_subtype_name from
        ( select d.*,c.wd_type,c.protocol,c.version,c.ip_address,c.port,c.com_port,c.protocol_flag,c.username,c.password from device d, device_wd c
        where d.device_type_no=7 and d.device_id=c.device_id and d.use=1 AND d.del_status = 0
        <if test="deviceId != null and deviceId != ''">AND d.device_id=#{deviceId}</if>
        <if test="deviceName != null &amp;&amp; deviceName != '' "> AND d.device_name like CONCAT('%', #{deviceName}, '%') </if>
        <if test="facilityNo != null &amp;&amp; facilityNo != '' "> AND d.facility_no =#{facilityNo}</if>
        <if test="use != null &amp;&amp; use>=0 "> AND d.use =#{use}</if>
        <if test="protocol != null &amp;&amp; protocol != '' "> AND c.protocol =#{protocol}</if>
        <if test="wdType != null &amp;&amp; wdType != '' "> AND c.wd_type =#{wdType}</if>
        ) dd
        left join direction e on dd.direction_no=e.direction_no
        left join device_subtype_dict dt on dd.wd_type=dt.device_subtype_no and dd.device_type_no=dt.device_type_no
        left join facility f on dd.facility_no=f.facility_no
        left join road r on r.road_no = f.road_no
        <if test="facilityTypeNo != null &amp;&amp; facilityTypeNo>0 "> where f.facility_type_no=#{facilityTypeNo} </if>
        order by dd.device_id desc
    </select>

    <update id="updateCms" parameterType="com.bt.itsoutfield.domain.dto.DeviceCmsDTO">
        update device_cms
        set
            width=#{width},
            height=#{height},
            cms_type=#{cmsType},
            protocol=#{protocol},
            version=#{version},
            ip_address=#{ipAddress},
            port=#{port},
            com_port=#{comPort},
            protocol_flag=#{protocolFlag},
            is_scl2008=#{isScl2008},
            position_id=#{positionId},
            lay_y=#{layY},
            lay_x=#{layX}
        where device_id=#{deviceId}
    </update>

    <update id="updateVd" parameterType="com.bt.itsoutfield.domain.dto.DeviceVdDTO">
        update device_vd
        set
            vd_type=#{vdType},
            protocol=#{protocol},
            version=#{version},
            ip_address=#{ipAddress},
            port=#{port},
            com_port=#{comPort},
            protocol_flag=#{protocolFlag},
            lane_num=#{laneNum}
        where device_id=#{deviceId}
    </update>

    <update id="updateWd" parameterType="com.bt.itsoutfield.domain.dto.DeviceWdDTO">
        update device_wd set
        <if test="wdType != null">wd_type=#{wdType},</if>
        protocol=#{protocol},
        <if test="version != null ">version=#{version},</if>
        ip_address=#{ipAddress},
        port=#{port},
        com_port=#{comPort},
        <if test="protocolFlag != null "> protocol_flag=#{protocolFlag}</if>
        where device_id=#{deviceId}
    </update>

    <select id="selectEventDirection" resultType="com.bt.itsoutfield.domain.entity.Event" parameterType="com.bt.itsoutfield.domain.entity.Event">
        select d.direction_no as directionNo, d.road_no as roadNo, d.road_line as roadLine from direction d
        where d.direction_no = #{directionNo}
        limit 1
    </select>

    <select id="selectEventDevice" resultType="com.bt.itsoutfield.domain.entity.Event" parameterType="com.bt.itsoutfield.domain.entity.Event">
        select d.device_id as deviceId from device d
        where d.direction_no = #{directionNo} and d.use = 1 AND d.del_status = 0
        <if test="deviceTypeNo != null &amp;&amp; deviceTypeNo != '' "> and d.device_type_no =#{deviceTypeNo}</if>
        <if test="roadLine != null &amp;&amp; roadLine = 0">and mp_value &lt;= #{mp_value} order by mp_value desc</if>
        <if test="roadLine != null &amp;&amp; roadLine = 1">and mp_value &gt;= #{mp_value} order by mp_value asc</if>
            limit 1
    </select>

        <select id="selectDeviceList" resultMap="deviceMap" parameterType="com.bt.itsoutfield.domain.dto.DeviceDTO">
        select f.facility_name,f.road_no,dd.*,r.road_name,
        e.direction_name from
        ( select d.status,d.device_name,d.mile_post,d.device_id,
        d.lng,d.lat,d.facility_no,d.device_type_no,d.direction_no from device d where 1=1
        <if test="ids != null &amp;&amp; ids.size()>0">
            and d.device_id in
            <foreach collection="ids" item="deviceId" index="index" open="(" close=")" separator=",">
                concat("",#{deviceId},"")
            </foreach>
        </if>
        ) dd
        left join direction e on dd.direction_no=e.direction_no
        left join facility f on dd.facility_no=f.facility_no
        left join road r on f.road_no=r.road_no
        order by dd.device_name asc
    </select>

    <select id="selectCmsInner" resultMap="deviceCmsMap" parameterType="com.bt.itsoutfield.domain.dto.DeviceCmsDTO">
        select f.facility_name,f.road_no,dd.*,r.road_alias,r.road_name,
        e.direction_name from
        ( select c.*,d.status,d.device_name,d.mile_post,d.source_id,
        d.lng,d.lat,d.facility_no,d.device_type_no,d.direction_no from device d, device_cms c
        <if test="roadNo != null &amp;&amp; roadNo > 0 ">,facility f2 </if>
        where d.device_type_no=2 and d.device_id=c.device_id and d.use=1 AND d.del_status = 0
        <if test="deviceName != null &amp;&amp; deviceName != '' "> AND d.device_name like CONCAT('%', #{deviceName}, '%')</if>
        <if test="deviceId != null"> AND d.device_id =#{deviceId}</if>
        <if test="facilityNo != null"> AND d.facility_no =#{facilityNo}</if>
        <if test="use != null &amp;&amp; use>=0 "> AND d.use =#{use}</if>
        <if test="status != null &amp;&amp; status>=0 "> AND d.status =#{status}</if>
        <if test="cmsType != null &amp;&amp; cmsType >=0 "> AND c.cms_type =#{cmsType}</if>
        <if test="width != null &amp;&amp; width>=0 "> AND c.width =#{width}</if>
        <if test="height != null &amp;&amp; height>=0 "> AND c.height =#{height}</if>
        <if test="roadNo != null &amp;&amp; roadNo > 0 "> and f2.road_no =#{roadNo} and f2.facility_no =d.facility_no </if>
        <if test="idList != null &amp;&amp; idList.size()>0">
            and d.device_id in
            <foreach collection="idList" item="deviceId" index="index" open="(" close=")" separator=",">
                concat("",#{deviceId},"")
            </foreach>
        </if>
        ) dd
        left join direction e on dd.direction_no=e.direction_no
        left join facility f on dd.facility_no=f.facility_no
        left join road r on f.road_no=r.road_no
        WHERE f.road_no is not null
        AND dd.mile_post is not NULL
        AND dd.device_name is not NULL
        order by e.road_line,dd.mile_post asc
    </select>

    <select id="selectOppositeDirection" resultType="java.lang.String" parameterType="com.bt.itsoutfield.domain.dto.DeviceCmsDTO">
        select direction_no from direction where road_no = #{roadNo} and direction_no != #{directionNo} and direction_name != '双向' limit 1
    </select>

    <resultMap type="com.bt.itsoutfield.domain.entity.Direction" id="directionMap">
        <result property="directionNo" column="direction_no"/>
        <result property="directionName" column="direction_name"/>
        <result property="roadNo" column="road_no"/>
        <result property="roadLine" column="road_line"/>
    </resultMap>

    <select id="getDirectionInfo" resultMap="directionMap" parameterType="com.bt.itsoutfield.domain.entity.Direction">
        select direction_no,direction_name,road_no,road_line from direction
        where direction_no = #{directionNo} limit 1
    </select>

    <select id="selectDeviceById" resultType="com.bt.itsoutfield.domain.vo.DeviceVO" parameterType="java.lang.String">
        select dd.device_name AS deviceName from
        ( select d.status,d.device_name,d.mile_post,d.device_id,
        d.lng,d.lat,d.facility_no,d.device_type_no,d.direction_no from device d where 1=1
        and d.use=1 AND d.del_status = 0
        and d.device_id=#{deviceId}
        ) dd
        left join direction e on dd.direction_no=e.direction_no
        left join facility f on dd.facility_no=f.facility_no
        left join road r on f.road_no=r.road_no
        order by dd.device_name asc
    </select>

    <select id="eventSelectCmsByLat" resultMap="deviceCmsMap">
        SELECT a.*,di.direction_name FROM (
        SELECT f.*,d.status,d.device_name,d.mile_post,d.source_id,d.lng,d.lat,d.device_type_no,d.direction_no,d.mp_value,dc.*,
        IFNULL(
        6371 * acos(
        cos(radians(#{lat}))
        * cos( radians( d.lat ) )
        * cos(radians( d.lng ) - radians(#{lon}))
        +
        sin( radians(#{lat}) )
        * sin( radians( d.lat ) )
        ),0
        ) AS distance
        FROM device d,(SELECT fa.facility_no,fa.facility_name,fa.facility_type_no,r.road_no,r.road_name,r.road_alias FROM facility fa,road r
        WHERE fa.road_no=r.road_no
        <if test="facilityTypeNos != null &amp;&amp; facilityTypeNos.size()>0">
            and fa.facility_type_no IN
            <foreach collection="facilityTypeNos" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="otherRoad != null and otherRoad == 0 and roadNo != null and roadNo > 0">
        	AND r.road_no=#{roadNo}
        </if>
        ) f,device_cms dc
        WHERE d.facility_no = f.facility_no AND d.device_id = dc.device_id AND d.lng IS NOT NULL AND d.del_status = 0 AND d.`use` = 1
        <if test="radius != null "> HAVING #{radius} > distance</if>
        ) a LEFT JOIN direction di ON a.direction_no=di.direction_no
        ORDER BY distance LIMIT 100
    </select>

    <select id="selectRoadName" resultType="String">
        SELECT road_name FROM road WHERE road_no = #{roadNo}
    </select>

</mapper>