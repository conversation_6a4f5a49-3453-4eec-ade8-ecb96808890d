<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsoutfield.mapper.CmsPlanMapper">
    <resultMap id="BaseResultMap" type="com.bt.itsoutfield.domain.entity.CmsPlan">
        <id column="id" property="id"/>
        <result column="plan_name" property="planName"/>
        <result column="plan_type" property="planType"/>
        <result column="company" property="company"/>
        <result column="location" property="location"/>
        <result column="direction" property="direction"/>
        <result column="range" property="range"/>
        <result column="plan_duration" property="planDuration"/>
        <result column="message" property="message"/>
    </resultMap>

    <select id="select" resultMap="BaseResultMap" parameterType="com.bt.itsoutfield.domain.entity.CmsPlan">
        select * from cms_plan
        where 1=1
        <if test="planType != null"> AND plan_type =#{planType}</if>
        <if test="company != null"> AND company like concat('%',#{company},'%')</if>
        <if test="planName != null"> AND plan_name like concat("%",#{planName},"%")</if>
        order by id asc
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.bt.itsoutfield.domain.entity.CmsPlan"
            useGeneratedKeys="true">
        insert into cms_plan (plan_name, plan_type, company, `location`, direction, `range`,
                              plan_duration, message)
        values (#{planName}, #{planType}, #{company}, #{location}, #{direction}, #{range},
                #{planDuration}, #{message})
    </insert>

    <update id="update" parameterType="com.bt.itsoutfield.domain.entity.CmsPlan">
        update cms_plan
        <set>
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="planType != null">plan_type = #{planType},</if>
            <if test="company != null">company = #{company},</if>
            <if test="location != null">`location` = #{location},</if>
            <if test="direction != null">direction = #{direction},</if>
            <if test="range != null">`range` = #{range},</if>
            <if test="planDuration != null">plan_duration = #{planDuration},</if>
            <if test="message != null">message = #{message},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="com.bt.itsoutfield.domain.entity.CmsPlan">
        delete c.* from cms_plan c where c.id = #{id}
    </delete>


    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into cms_plan
                (plan_name, plan_type, company, `location`, direction, `range`, plan_duration, message)
                values
        <foreach collection="list" item="item" separator=",">
            (#{item.planName}, #{item.planType}, #{item.company}, #{item.location}, #{item.direction},
             #{item.range}, #{item.planDuration}, #{item.message})
        </foreach>
    </insert>
</mapper>