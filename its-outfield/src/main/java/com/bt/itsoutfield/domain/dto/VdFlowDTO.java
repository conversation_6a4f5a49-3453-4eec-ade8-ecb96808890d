package com.bt.itsoutfield.domain.dto;

import javax.validation.constraints.NotBlank;
import java.util.List;

public class VdFlowDTO {
	private String facilityNo;//安装位置
	private String deviceId;//设备id
	private String vdModel;	//交调站类别，前端名称（数据库值）:一类设备（"1级设备"），二类设备（"2级设备"）
	@NotBlank(message = "查询开始时间不能为空")
	private String createDateStart; //查询开始时间
	@NotBlank(message = "查询结束时间不能为空")
    private String createDateEnd;   //查询结束时间
    private Integer vdType;	//设备型号，1车检器，7一类交调站，8二类交调站
    @NotBlank(message = "统计方式不能为空")
    private String excelType;//报表类型：tvstypeflow-月度车型量 tvsyearflow-年度车流量 tvsyeartype-年度车型量
    private String vdDirection;
    private String writeDate;// datetime DEFAULT NULL COMMENT '时间日期',
    private String roadNo;	//归属路段的编号，对应road表中的ROAD_NO  [queryLastData]

    private Integer yearQuery;//统计年份
	private String monthQuery;//统计月份
	private String dateFormat;//时间格式 '%Y-%m-%d %H:00' 日报表 '%Y-%m-%d'月报表 '%Y-%m' 年报表 '%Y'历年报表
	private List<String> facilities;
	private List<String> idList;
	private String deviceName;
	private Integer sourceId;
	public String getDeviceId() {
		return deviceId;
	}
	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}
	public String getFacilityNo() {
		return facilityNo;
	}
	public void setFacilityNo(String facilityNo) {
		this.facilityNo = facilityNo;
	}
	public String getVdModel() {
		return vdModel;
	}
	public void setVdModel(String vdModel) {
		this.vdModel = vdModel;
	}
	public String getCreateDateStart() {
		return createDateStart;
	}
	public void setCreateDateStart(String createDateStart) {
		this.createDateStart = createDateStart;
	}
	public String getCreateDateEnd() {
		return createDateEnd;
	}
	public void setCreateDateEnd(String createDateEnd) {
		this.createDateEnd = createDateEnd;
	}
	public Integer getVdType() {
		return vdType;
	}
	public void setVdType(Integer vdType) {
		this.vdType = vdType;
	}
	public String getExcelType() {
		return excelType;
	}
	public void setExcelType(String excelType) {
		this.excelType = excelType;
	}
	public String getVdDirection() {
		return vdDirection;
	}
	public void setVdDirection(String vdDirection) {
		this.vdDirection = vdDirection;
	}
	public String getWriteDate() {
		return writeDate;
	}
	public void setWriteDate(String writeDate) {
		this.writeDate = writeDate;
	}
	public Integer getYearQuery() {
		return yearQuery;
	}
	public void setYearQuery(Integer yearQuery) {
		this.yearQuery = yearQuery;
	}
	public String getMonthQuery() {
		return monthQuery;
	}
	public void setMonthQuery(String monthQuery) {
		this.monthQuery = monthQuery;
	}
	public String getDateFormat() {
		return dateFormat;
	}
	public void setDateFormat(String dateFormat) {
		this.dateFormat = dateFormat;
	}
	public String getRoadNo() {
		return roadNo;
	}
	public void setRoadNo(String roadNo) {
		this.roadNo = roadNo;
	}

	public List<String> getFacilities() {
		return facilities;
	}

	public void setFacilities(List<String> facilities) {
		this.facilities = facilities;
	}

	public List<String> getIdList() {
		return idList;
	}

	public void setIdList(List<String> idList) {
		this.idList = idList;
	}

	public String getDeviceName() {
		return deviceName;
	}

	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}

	public Integer getSourceId() {
		return sourceId;
	}

	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}
}
