package com.bt.itsoutfield.domain.dto;


import java.util.List;

public class CmsRtRepertoryDTO {
    private String id;				//主键
    private String name;			//节目名称

    private String pageId;			//归属页id
    private String nodeId;			//归属业中的子项id

    private Integer labelX;		//文本坐标
    private Integer labelY;
    private Integer fontSize;		//字号
    private String fontType;		//字体
    private String fontColor;		//字体颜色
    private String messageBody;		//文本内容

    private String multiBody;		//图片内容--相对路径

    private String backColor;		//背景颜色
    private Integer holdTime;		//停留时间
    private Integer speed;			//变换速度
    private String inputMode;		//入屏方式
    private String outputMode;		//出屏方式

    private String layout;			//布局方式

    private String deviceId;		//设备NO-device表主键
    private String deviceName;		//设备名称
    private String releaseTime;	//发布时间

    private List<String> idList;
    private List<CmsRtRepertoryDTO> playList;
    private String roadNo;
    private String milePost;
    private Integer cmsType;
    private String facilityNo;
    private String orgId;

    private String startDate;
    private String endDate;
    private Integer flag;
    private String userName;
    private String facilityNos;
    private List<String> facilities;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public Integer getLabelX() {
        return labelX;
    }

    public void setLabelX(Integer labelX) {
        this.labelX = labelX;
    }

    public Integer getLabelY() {
        return labelY;
    }

    public void setLabelY(Integer labelY) {
        this.labelY = labelY;
    }

    public Integer getFontSize() {
        return fontSize;
    }

    public void setFontSize(Integer fontSize) {
        this.fontSize = fontSize;
    }

    public String getFontType() {
        return fontType;
    }

    public void setFontType(String fontType) {
        this.fontType = fontType;
    }

    public String getFontColor() {
        return fontColor;
    }

    public void setFontColor(String fontColor) {
        this.fontColor = fontColor;
    }

    public String getMessageBody() {
        return messageBody;
    }

    public void setMessageBody(String messageBody) {
        this.messageBody = messageBody;
    }

    public String getMultiBody() {
        return multiBody;
    }

    public void setMultiBody(String multiBody) {
        this.multiBody = multiBody;
    }

    public String getBackColor() {
        return backColor;
    }

    public void setBackColor(String backColor) {
        this.backColor = backColor;
    }

    public Integer getHoldTime() {
        return holdTime;
    }

    public void setHoldTime(Integer holdTime) {
        this.holdTime = holdTime;
    }

    public Integer getSpeed() {
        return speed;
    }

    public void setSpeed(Integer speed) {
        this.speed = speed;
    }

    public String getInputMode() {
        return inputMode;
    }

    public void setInputMode(String inputMode) {
        this.inputMode = inputMode;
    }

    public String getOutputMode() {
        return outputMode;
    }

    public void setOutputMode(String outputMode) {
        this.outputMode = outputMode;
    }

    public String getLayout() {
        return layout;
    }

    public void setLayout(String layout) {
        this.layout = layout;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(String releaseTime) {
        this.releaseTime = releaseTime;
    }

    public List<String> getIdList() {
        return idList;
    }

    public void setIdList(List<String> idList) {
        this.idList = idList;
    }

    public List<CmsRtRepertoryDTO> getPlayList() {
        return playList;
    }

    public void setPlayList(List<CmsRtRepertoryDTO> playList) {
        this.playList = playList;
    }

    public String getRoadNo() {
        return roadNo;
    }

    public void setRoadNo(String roadNo) {
        this.roadNo = roadNo;
    }

    public String getMilePost() {
        return milePost;
    }

    public void setMilePost(String milePost) {
        this.milePost = milePost;
    }

    public Integer getCmsType() {
        return cmsType;
    }

    public void setCmsType(Integer cmsType) {
        this.cmsType = cmsType;
    }

    public String getFacilityNo() {
        return facilityNo;
    }

    public void setFacilityNo(String facilityNo) {
        this.facilityNo = facilityNo;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getStartDate() {
            return startDate;
        }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }


    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public List<String> getFacilities() {
        return facilities;
    }

public String getFacilityNos() {
    return facilityNos;
}

public void setFacilityNos(String facilityNos) {
    this.facilityNos = facilityNos;
}

public void setFacilities(List<String> facilities) {
        this.facilities = facilities;
    }

}
