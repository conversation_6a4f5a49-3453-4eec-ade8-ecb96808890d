package com.bt.itsoutfield.domain.dto;


/**
 * 设备-车检器
 * 
 * <AUTHOR>
 * @date 2021-03-23 10:07:00
 */
public class DeviceVdDTO extends DeviceDTO {

	/**
	 * 设备id  - device表主键
	 */
	private String deviceId;
	/**
	 * 车检器类型（1微波车辆检测器  2红外车辆检测器  3超声波车辆检测器 4视频车辆检测器 5I类交调站  6II类交调站）
	 */
	private Integer vdType;
	/**
	 * 厂家控制协议(钛星微波,交调站,辽宁金洋车检器,无锡润德,浙大中控,哈工大)
	 */
	private String protocol;
	/**
	 * 1级设备 or 2级设备
	 */
	private String vdModel;
	/**
	 * 版本号
	 */
	private String version;
	/**
	 * IP地址
	 */
	private String ipAddress;
	/**
	 * 通讯端口
	 */
	private Integer port;
	/**
	 * 串口号
	 */
	private Integer comPort;
	/**
	 * 通讯方式：1串口模式 2OPC模式 3TCP模式 4UDP模式 5第三方协议模式
	 */
	private Integer protocolFlag;
	/**
	 * 车道数
	 */
	private Integer laneNum;
	/**
	 * 实时数据展示框高度
	 */
	private Integer height;
	/**
	 * 实时数据展示框宽度
	 */
	private Integer width;
	/**
	 * 在路段监控中,相对底图的Y轴坐标
	 */
	private Integer layY;
	/**
	 * 在路段监控中,相对底图的X轴坐标
	 */
	private Integer layX;

	/**
	 * 设置：设备id  - device表主键
	 */
	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}
	/**
	 * 获取：设备id  - device表主键
	 */
	public String getDeviceId() {
		return deviceId;
	}
	/**
	 * 设置：车检器类型（1微波车辆检测器  2红外车辆检测器  3超声波车辆检测器 4视频车辆检测器 5I类交调站  6II类交调站）
	 */
	public void setVdType(Integer vdType) {
		this.vdType = vdType;
	}
	/**
	 * 获取：车检器类型（1微波车辆检测器  2红外车辆检测器  3超声波车辆检测器 4视频车辆检测器 5I类交调站  6II类交调站）
	 */
	public Integer getVdType() {
		return vdType;
	}
	/**
	 * 设置：厂家控制协议(钛星微波,交调站,辽宁金洋车检器,无锡润德,浙大中控,哈工大)
	 */
	public void setProtocol(String protocol) {
		this.protocol = protocol;
	}
	/**
	 * 获取：厂家控制协议(钛星微波,交调站,辽宁金洋车检器,无锡润德,浙大中控,哈工大)
	 */
	public String getProtocol() {
		return protocol;
	}
	/**
	 * 设置：1级设备 or 2级设备
	 */
	public void setVdModel(String vdModel) {
		this.vdModel = vdModel;
	}
	/**
	 * 获取：1级设备 or 2级设备
	 */
	public String getVdModel() {
		return vdModel;
	}
	/**
	 * 设置：版本号
	 */
	public void setVersion(String version) {
		this.version = version;
	}
	/**
	 * 获取：版本号
	 */
	public String getVersion() {
		return version;
	}
	/**
	 * 设置：IP地址
	 */
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
	/**
	 * 获取：IP地址
	 */
	public String getIpAddress() {
		return ipAddress;
	}
	/**
	 * 设置：通讯端口
	 */
	public void setPort(Integer port) {
		this.port = port;
	}
	/**
	 * 获取：通讯端口
	 */
	public Integer getPort() {
		return port;
	}
	/**
	 * 设置：串口号
	 */
	public void setComPort(Integer comPort) {
		this.comPort = comPort;
	}
	/**
	 * 获取：串口号
	 */
	public Integer getComPort() {
		return comPort;
	}
	/**
	 * 设置：通讯方式：1串口模式 2OPC模式 3TCP模式 4UDP模式 5第三方协议模式
	 */
	public void setProtocolFlag(Integer protocolFlag) {
		this.protocolFlag = protocolFlag;
	}
	/**
	 * 获取：通讯方式：1串口模式 2OPC模式 3TCP模式 4UDP模式 5第三方协议模式
	 */
	public Integer getProtocolFlag() {
		return protocolFlag;
	}
	/**
	 * 设置：车道数
	 */
	public void setLaneNum(Integer laneNum) {
		this.laneNum = laneNum;
	}
	/**
	 * 获取：车道数
	 */
	public Integer getLaneNum() {
		return laneNum;
	}
	/**
	 * 设置：实时数据展示框高度
	 */
	public void setHeight(Integer height) {
		this.height = height;
	}
	/**
	 * 获取：实时数据展示框高度
	 */
	public Integer getHeight() {
		return height;
	}
	/**
	 * 设置：实时数据展示框宽度
	 */
	public void setWidth(Integer width) {
		this.width = width;
	}
	/**
	 * 获取：实时数据展示框宽度
	 */
	public Integer getWidth() {
		return width;
	}
	/**
	 * 设置：在路段监控中,相对底图的Y轴坐标
	 */
	public void setLayY(Integer layY) {
		this.layY = layY;
	}
	/**
	 * 获取：在路段监控中,相对底图的Y轴坐标
	 */
	public Integer getLayY() {
		return layY;
	}
	/**
	 * 设置：在路段监控中,相对底图的X轴坐标
	 */
	public void setLayX(Integer layX) {
		this.layX = layX;
	}
	/**
	 * 获取：在路段监控中,相对底图的X轴坐标
	 */
	public Integer getLayX() {
		return layX;
	}
}
