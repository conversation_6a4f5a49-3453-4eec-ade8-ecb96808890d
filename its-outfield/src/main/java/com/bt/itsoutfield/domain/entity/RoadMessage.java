package com.bt.itsoutfield.domain.entity;


import com.bt.itsoutfield.domain.vo.CmsRtRepertoryVO;
import com.bt.itsoutfield.domain.vo.TvsFlowVO;
import com.bt.itsoutfield.domain.vo.VdFlowVO;
import com.bt.itsoutfield.domain.vo.WdFluxVO;

import java.util.List;

public class RoadMessage {
	private String id;			//消息ID
	private String name;		//消息名称
	private String dev_type;	//设备类型
	private Integer msg_type;	//消息类型，0：状态类，1：信息类
	private String device_id;	//消息对应的设备ID
	private Integer online;		//在线状态，0：离线，1：在线
	
	private CmsRtRepertoryVO cmsRtRepertory;
	private VdFlowVO vdCarType;
	private List<TvsFlowVO> tvsFlowList;
	private WdFluxVO wdFlux;
	
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getDevice_id() {
		return device_id;
	}
	public void setDevice_id(String device_id) {
		this.device_id = device_id;
	}
	public String getDev_type() {
		return dev_type;
	}
	public void setDev_type(String dev_type) {
		this.dev_type = dev_type;
	}
	public Integer getMsg_type() {
		return msg_type;
	}
	public void setMsg_type(Integer msg_type) {
		this.msg_type = msg_type;
	}
	public Integer getOnline() {
		return online;
	}
	public void setOnline(Integer online) {
		this.online = online;
	}
	public CmsRtRepertoryVO getCmsRtRepertory() {
		return cmsRtRepertory;
	}
	public void setCmsRtRepertory(CmsRtRepertoryVO cmsRtRepertory) {
		this.cmsRtRepertory = cmsRtRepertory;
	}
	public VdFlowVO getVdCarType() {
		return vdCarType;
	}
	public void setVdCarType(VdFlowVO vdCarType) {
		this.vdCarType = vdCarType;
	}
	public List<TvsFlowVO> getTvsFlowList() {
		return tvsFlowList;
	}
	public void setTvsFlowList(List<TvsFlowVO> tvsFlowList) {
		this.tvsFlowList = tvsFlowList;
	}
	public WdFluxVO getWdFlux() {
		return wdFlux;
	}
	public void setWdFlux(WdFluxVO wdFlux) {
		this.wdFlux = wdFlux;
	}
}
