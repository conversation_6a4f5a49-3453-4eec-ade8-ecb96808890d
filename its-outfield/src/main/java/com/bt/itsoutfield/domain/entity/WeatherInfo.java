package com.bt.itsoutfield.domain.entity;

import com.bt.itsoutfield.domain.vo.WdFluxVO;

public class WeatherInfo {
    private String deviceId;
    private String deviceName;
    private String milePost;
    private String roadName;
    private String directionName;
    private Integer status;
    private Double lat;
    private Double lng;
    private WdFluxVO wdFluxVO;
    private Double temp;
    private Double humi;
    private Integer visibility;
    private String vane;
    private Double windSpeed;
    private double[] today24HourTemp;//24小时温度变化曲线
    private int[] today24HourVisi;//24小时能见度变化曲线
    private double[] today24HourRain;//24小时降水变化曲线
    private double[] today4HourRain;//当前1小时降水变化
    private String time;

    private String roadTemperature;
    private String roadStatus;

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getMilePost() {
        return milePost;
    }

    public void setMilePost(String milePost) {
        this.milePost = milePost;
    }

    public String getRoadName() {
        return roadName;
    }

    public void setRoadName(String roadName) {
        this.roadName = roadName;
    }

    public String getDirectionName() {
        return directionName;
    }

    public void setDirectionName(String directionName) {
        this.directionName = directionName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public WdFluxVO getWdFluxVO() {
        return wdFluxVO;
    }

    public void setWdFluxVO(WdFluxVO wdFluxVO) {
        this.wdFluxVO = wdFluxVO;
    }

    public double[] getToday24HourTemp() {
        return today24HourTemp;
    }

    public void setToday24HourTemp(double[] today24HourTemp) {
        this.today24HourTemp = today24HourTemp;
    }

    public int[] getToday24HourVisi() {
        return today24HourVisi;
    }

    public void setToday24HourVisi(int[] today24HourVisi) {
        this.today24HourVisi = today24HourVisi;
    }

    public double[] getToday24HourRain() {
        return today24HourRain;
    }

    public void setToday24HourRain(double[] today24HourRain) {
        this.today24HourRain = today24HourRain;
    }

    public double[] getToday4HourRain() {
        return today4HourRain;
    }

    public void setToday4HourRain(double[] today4HourRain) {
        this.today4HourRain = today4HourRain;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getTemp() {
        return temp;
    }

    public void setTemp(Double temp) {
        this.temp = temp;
    }

    public Double getHumi() {
        return humi;
    }

    public void setHumi(Double humi) {
        this.humi = humi;
    }

    public Integer getVisibility() {
        return visibility;
    }

    public void setVisibility(Integer visibility) {
        this.visibility = visibility;
    }

    public String getVane() {
        return vane;
    }

    public void setVane(String vane) {
        this.vane = vane;
    }

    public Double getWindSpeed() {
        return windSpeed;
    }

    public void setWindSpeed(Double windSpeed) {
        this.windSpeed = windSpeed;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getRoadTemperature() {
        return roadTemperature;
    }

    public void setRoadTemperature(String roadTemperature) {
        this.roadTemperature = roadTemperature;
    }

    public String getRoadStatus() {
        return roadStatus;
    }

    public void setRoadStatus(String roadStatus) {
        this.roadStatus = roadStatus;
    }
}
