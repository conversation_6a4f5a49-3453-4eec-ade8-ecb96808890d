package com.bt.itsoutfield.utils;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.ResourceUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Calendar;
import java.util.List;
import java.util.UUID;

public class PoiExcel {
	//返回单元格的起始和结束行
	public static Integer[] getRowRange(List<CellRangeAddress> listCombineCell,Cell cell)
            throws Exception{
        int firstC = 0;
        int lastC = 0;
        int firstR = 0;
        int lastR = 0;
        Integer[] rowRange = new Integer [2];
        for(CellRangeAddress ca:listCombineCell)
        {
            //获得合并单元格的起始行, 结束行, 起始列, 结束列
            firstC = ca.getFirstColumn();
            lastC = ca.getLastColumn();
            firstR = ca.getFirstRow();
            lastR = ca.getLastRow();
            if(cell.getRowIndex() >= firstR && cell.getRowIndex() <= lastR)
            {
                if(cell.getColumnIndex() >= firstC && cell.getColumnIndex() <= lastC)
                {                    
                    rowRange[0]=firstR;
                	rowRange[1]=lastR;
                    break;
                }
            }
            else
            {   
            	rowRange[0]=cell.getRowIndex();
            	rowRange[1]=cell.getRowIndex();
            }
        }
        return rowRange;
    }

	public static Workbook getWbTemplate(String inputfilepath)
			throws IOException {
		File inputfile = new File(inputfilepath);
		Workbook wb = null;
		if (inputfile.exists()) {
			FileInputStream in = new FileInputStream(inputfile);
			if (inputfile.getName().endsWith("xls")) { // Excel&nbsp;2003
				System.out.println("xls");
				wb = new HSSFWorkbook(in);
			} else if (inputfile.getName().endsWith("xlsx")) { // Excel	// 2007/2010
				System.out.println("xlsx");
				wb = new XSSFWorkbook(in);
			}
		}
		return wb;
	}

	// excel模板为31天的，假设是2月有28天，那么deldaynum=31-28=3天，删除9行，返回workbook
	public static Workbook deleteMonthDays(int deldaynum, Workbook workbook) {
		for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
			Sheet sheet = workbook.getSheetAt(i);
			for (int j = 95; j > 95 - deldaynum * 3; j--) { // 清空该月没有的天数
				sheet.removeRow(sheet.getRow(j - 1));
			}
			sheet.shiftRows(sheet.getLastRowNum(), sheet.getLastRowNum(),
					-deldaynum * 3); // 把最后一行往上平移并调整行高
			if (deldaynum != 0) {
				sheet.getRow(95 - 3 * deldaynum).setHeightInPoints(
						(float) 111.8);
				sheet.getRow(95).setHeightInPoints((float) 15.6);
			}
		}
		return workbook;
	}

	//
	public static Workbook getWorkBook(int roadnum, int year, int month)
			throws IOException {
		// 1.算出这个月差31天几天
		Calendar c = Calendar.getInstance();
		c.set(Calendar.YEAR, year);
		c.set(Calendar.MONTH, month - 1);
		int diff = 31 - c.getActualMaximum(Calendar.DAY_OF_MONTH);
		// 2.根据roadnum选择不同的filepath从而生成不同的模板workbook
		String excelfilename="/template/liuhe31.xls";

		if (roadnum == 1) {
			excelfilename = "/template/yizhou31.xls";
		}else if (roadnum == 2) {
			excelfilename = "/template/donglan31.xls";
		}else if (roadnum == 3) {
			excelfilename = "/template/duanbei31.xls";
		}
		String inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+excelfilename;
		Workbook workBook = getWbTemplate(inputfilepath);
		// 3.把第2步生成的workbook根据这个月的天数传入方法调整模板，最终返回
		workBook = deleteMonthDays(diff, workBook);
		return workBook;
	}
	
	public static Workbook getWorkBook2(int fileflag)
			throws IOException {
		// 1.根据fileflag选择不同的filepath从而生成不同的模板workbook 0表示roadcheck，1表示tollstationcheck
		String inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/roadcheck.xls";
		if (fileflag == 1) {
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/tollstationcheck.xls";
		}
		if (fileflag == 2) {
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/cscstatistics.xls";
		}
		if (fileflag == 3) {
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/cscstatistics.xls";
		}
		if (fileflag == 4) {
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/tvstypeflow1.xls";
		}
		if (fileflag == 5) {
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/tvstypeflow2.xls";
		}
		if (fileflag == 6) {
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/tunndevicecheck_duanbei";
		}
		if (fileflag == 7) {//交调站年车型量1类统计
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/tvsyeartypeflow1.xls";
		}
		if (fileflag == 8) {//交调站年车型量2类统计
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/tvsyeartypeflow2.xls";
		}
		if (fileflag == 9) {//交调站年车流量统计表
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/tvsyearflow.xls";
		}
		if (fileflag == 10) {//车检器年车流量统计表
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/vdyearflow.xls";
		}
		if (fileflag == 11) {//车检器车流量统计表
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/vdyeartype.xls";
		}
		if (fileflag == 12) {//气象报表
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/wdday.xls";
		}
		if (fileflag == 13) {//情报板实时节目
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/cmsrtprogram.xls";
		}
		if (fileflag == 14) {//情报板发布日志
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/cmslogprogram.xls";
		}
		if (fileflag == 15) {//外场设备离线统计
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/roaddevicecheck.xls";
		}
		if (fileflag == 16) {//外场设备状态统计
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/roaddevicestatus.xls";
		}
		if (fileflag == 17) {//设路施工统计
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/constructioninfo.xls";
		}
		if (fileflag == 18) {//天气上报统计
			inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/weatherinfo.xls";
		}
		Workbook workBook = getWbTemplate(inputfilepath);
		return workBook;
	}
	
	public static Workbook getWorkBookbyname(String filename)
			throws IOException {
		// 1.根据fileflag选择不同的filepath从而生成不同的模板workbook 0表示roadcheck，1表示tollstationcheck
		String inputfilepath = PoiExcel.class.getClassLoader().getResource("").getPath()+"/template/"+filename;
		Workbook workBook = getWbTemplate(inputfilepath);
		return workBook;
	}

	public static int calrownum(String date_string) {
		int rownum = 0;
		// 1.提取“15日1班次”中的两个数字
		String[] arr = date_string.replace("日", ",").replace("班次", "")
				.split(",");
		int d = NumberUtils.toInt(arr[0]);
		int s = NumberUtils.toInt(arr[1]);
		// 2.根据3d+s-2公式算出行数
		rownum = 3 * d + s - 2;
		return rownum;
	}
	
	//输出excel到浏览器上
	public static void ouputExcel(Workbook workBook,String FileName,HttpServletRequest request,HttpServletResponse response){
		File path = null;
		try {
			path = new File(ResourceUtils.getURL("classpath:").getPath());
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		}
		if(!path.exists()) path = new File("");
		String filePath = path.getAbsolutePath()+"/" + "download" + File.separator +FileName + "##" + UUID.randomUUID().toString() + "%%.xls";
//	    System.out.println("filePath: "+filePath);
		FileOutputStream out = null;
		// 暂存到webapp下的download文件夹
		try {
			File outFile = new File(filePath); // 导出文档的存放位置
			out = new FileOutputStream(outFile);
			workBook.write(out);
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				if (out != null) {
					out.flush();
					out.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		downloadFile(filePath,response);
        delfile(filePath);
	}
	
	 private static void downloadFile(String filePath,HttpServletResponse response){
	    	try {
	            File file = new File(filePath);
	            String fileName = filePath.substring(filePath.lastIndexOf(File.separator)+1);//得到文件名
	            fileName=fileName.substring(0, fileName.indexOf("##"))+fileName.substring(fileName.indexOf("%%")+2,fileName.length());//下载时把文件名内的uuid码去掉
	            fileName = new String(fileName.getBytes("UTF-8"),"ISO8859-1");//把文件名按UTF-8取出并按ISO8859-1编码，保证弹出窗口中的文件名中文不乱码，中文不要太多，最多支持17个中文，因为header有150个字节限制。
	            response.setContentType("application/octet-stream");//告诉浏览器输出内容为流
	            response.addHeader("Content-Disposition", "attachment;filename="+fileName);//Content-Disposition中指定的类型是文件的扩展名，并且弹出的下载对话框中的文件类型图片是按照文件的扩展名显示的，点保存后，文件以filename的值命名，保存类型以Content中设置的为准。注意：在设置Content-Disposition头字段之前，一定要设置Content-Type头字段。
	            String len = String.valueOf(file.length());
	            response.setHeader("Content-Length", len);//设置内容长度
	            OutputStream out2 = response.getOutputStream();
	            IOUtils.write(FileUtils.readFileToByteArray(file), out2);
	            out2.close();
	        } catch (FileNotFoundException e) {
	            e.printStackTrace();
	        } catch (IOException e) {
	            e.printStackTrace();
	        }
	    }
	    
	    private static void delfile(String filePath){
	    	File file=new File(filePath);
	    	if(file.exists()&&file.isFile())
	    	{
	    		file.delete();
	    	}
	    }
	
}
