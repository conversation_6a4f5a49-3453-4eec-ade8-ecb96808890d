package com.bt.itsdevice.mapper;

import com.bt.itsdevice.domain.dto.FacilitySaDTO;
import com.bt.itsdevice.domain.dto.FacilitySaElementDTO;
import com.bt.itsdevice.domain.dto.ValidList;
import com.bt.itsdevice.domain.vo.FacilitySaElementVO;
import com.bt.itsdevice.domain.vo.FacilitySaVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FacilitySaMapper {

    List<FacilitySaVO> selectList (FacilitySaDTO facilitySaDTO);

    FacilitySaVO maxFacilityNo();

    int add(FacilitySaDTO facilitySaDTO);

    int update(FacilitySaDTO facilitySaDTO);

    int delete(FacilitySaDTO facilitySaDTO);

    int batchDelete(FacilitySaDTO facilitySaDTO);

	List<FacilitySaVO> selectAll(FacilitySaDTO facilitySaDTO);

    int updateSaElementSite(@Param("list") ValidList<FacilitySaElementDTO> saElementDtoList);

    List<FacilitySaElementVO> querySaElementsByFacility(String facilityNo);

    String queryFacilityDirection(String facilityNo, String direction);

    FacilitySaVO selectFacilityByNo(String facilityNo);

    List<FacilitySaVO> selectFacilityByElements();

    List<FacilitySaVO> selectBayonetSa();
}
