package com.bt.itsdevice.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.bt.itsdevice.domain.dto.ChargingpileRtDTO;
import com.bt.itsdevice.domain.vo.ChargingpileRtDataVO;

@Mapper
public interface ChargingpileMapper {

	ChargingpileRtDataVO statRtData(ChargingpileRtDTO dto);

	List<ChargingpileRtDataVO> selectRtDataList(ChargingpileRtDTO dto);

	ChargingpileRtDataVO statRtStatus(ChargingpileRtDTO dto);

}
