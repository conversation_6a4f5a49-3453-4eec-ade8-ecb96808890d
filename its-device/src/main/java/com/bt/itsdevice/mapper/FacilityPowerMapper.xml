<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsdevice.mapper.FacilityPowerMapper">
	<resultMap id="facilityPowerMap" type="com.bt.itscore.domain.vo.FacilityPowerVO" >
		<id property="facilityNo" column="facility_no"></id>
		<result property="facilityName" column="facility_name" ></result>
		<result property="roadNo" column="road_no" ></result>
		<result property="facilityTypeNo" column="facility_type_no" ></result>
		<result property="milePost" column="mile_post" ></result>
		<result property="upFacilityNo" column="up_facility_no" ></result>
		<result property="mpValue" column="mp_value" ></result>
		<result property="sort" column="sort" ></result>
		<result property="lng" column="lng" ></result>
		<result property="lat" column="lat" ></result>
		<result property="computeSwitch" column="compute_switch" ></result>
		<result property="roadName"  column="road_name"></result>
		<result property="facilityCode" column="facility_code" ></result>		
		<result property="type" column="type"/>	
		<result property="transformers" column="transformers"/>	
		<result property="installCapacity" column="install_capacity"/>
		<result property="reportCapacity" column="report_capacity"/>
		<result property="upFacilityName" column="up_facility_name"/>	
    	<collection property="voltageLevels" javaType="java.util.ArrayList"
			select="selectVoltageLevel" column="{facilityNo=facility_no}">
    	</collection>
    	<collection property="backupPowers" javaType="java.util.ArrayList"
			select="selectBackupPower" column="{facilityNo=facility_no}">
    	</collection>
    	<collection property="attachs" ofType="com.bt.itscore.domain.vo.FacilityAttachVO"
			select="selectAttach" column="{facilityNo=facility_no}">
    	</collection>
    </resultMap>

 	<resultMap type="com.bt.itscore.domain.vo.FacilityAttachVO" id="AttachMap">
		<id column="id" property="id"/>
		<result column="facility_no" property="facilityNo"/>
		<result column="file_name" property="fileName"/>
		<result column="disk_file_name" property="diskFileName"/>
		<result column="file_size" property="fileSize"/>
		<result column="content_type" property="contentType"/>
		<result column="digest" property="digest"/>
		<result column="disk_directory" property="diskDirectory"/>
		<result column="create_time" property="createTime"/>
	</resultMap>

	<resultMap type="com.bt.itscore.domain.dto.PowerCmdDTO" id="PowerCmdMap">
		<result column="facility_no" property="facilityNo"/>
		<result column="facility_name" property="facilityName"/>
		<result column="signal_id" property="signalId"/>
		<result column="name" property="name"/>
		<result column="data_value" property="dataValue"/>
		<result column="use" property="use"/>
	</resultMap>


	<select id="selectAttach" parameterType="com.bt.itsdevice.domain.dto.FacilityDTO" resultMap="AttachMap">
		SELECT * FROM facility_attach WHERE facility_no=#{facilityNo}	
	</select>   

	<select id="selectPowerAll" parameterType="com.bt.itscore.domain.dto.FacilityCommonDTO" resultMap="facilityPowerMap">
		select a.*,t2.facility_name up_facility_name,r.road_alias,r.road_name
		from (
		select f.facility_no,f.facility_name,f.road_no,f.up_facility_no,f.sort,f.mile_post,f.facility_type_no,f.lng,f.lat,f.compute_switch,fs.type,fs.transformers,
		fs.install_capacity,fs.report_capacity
		from facility f, facility_power fs
		where f.facility_type_no=44 AND f.facility_no=fs.facility_no AND f.facility_no IN (Facility-Permissions-Check)
		) a 
		inner join 
		(select facility_no,facility_name from facility 
		where 1=1
		<if test="facilityTypeNo != null &amp;&amp; facilityTypeNo > 0 "> AND facility_type_no=#{facilityTypeNo}</if>
		) t2 on a.up_facility_no=t2.facility_no
		left join road r on a.road_no=r.road_no
		order by a.road_no,a.facility_no
	</select>
	<select id="selectList" parameterType="com.bt.itsdevice.domain.dto.FacilityPowerDTO" resultMap="facilityPowerMap">
		select a.facility_no,a.facility_name,a.road_no,r.road_alias,r.road_name,a.up_facility_no,a.sort,a.mile_post,a.facility_type_no,a.lng,a.lat,a.compute_switch,
		a.type,a.transformers,a.install_capacity,a.report_capacity,f2.facility_name up_facility_name
		from (
			select f.facility_no,f.facility_name,f.road_no,f.up_facility_no,f.sort,f.mile_post,f.facility_type_no,f.lng,f.lat,f.compute_switch,fs.type,fs.transformers,
			fs.install_capacity,fs.report_capacity
			from facility f, facility_power fs
			where f.facility_type_no=44 AND f.facility_no=fs.facility_no AND f.facility_no IN (Facility-Permissions-Check)
			<if test="facilityName != null &amp;&amp; facilityName != '' "> AND f.facility_name like CONCAT('%', #{facilityName}, '%') </if>
			<if test="roadNo != null &amp;&amp; roadNo > 0 "> AND f.road_no=#{roadNo}</if>
			) a left OUTER JOIN facility f2 on a.up_facility_no=f2.facility_no
		left join road r on a.road_no=r.road_no
		order by a.road_no,a.facility_no
	</select>

	<resultMap id="treeMap" type="com.bt.itscore.domain.vo.FacilityPowerVO" >
		<id property="facilityNo" column="facility_no"></id>
		<result property="facilityName" column="facility_name" ></result>
		<result property="roadNo" column="road_no" ></result>
		<result property="facilityTypeNo" column="facility_type_no" ></result>
		<result property="milePost" column="mile_post" ></result>
		<result property="upFacilityNo" column="up_facility_no" ></result>
		<result property="mpValue" column="mp_value" ></result>
		<result property="sort" column="sort" ></result>
		<result property="lng" column="lng" ></result>
		<result property="lat" column="lat" ></result>
		<result property="computeSwitch" column="compute_switch" ></result>
		<result property="roadName"  column="road_name"></result>
		<result property="facilityCode" column="facility_code" ></result>		
		<result property="type" column="type"/>	
		<result property="transformers" column="transformers"/>	
		<result property="installCapacity" column="install_capacity"/>
		<result property="reportCapacity" column="report_capacity"/>
		<result property="upFacilityName" column="up_facility_name"/>
		<result property="upFacilityTypeNo" column="up_facility_type_no"/>	
    </resultMap>

	<select id="selectAll" parameterType="com.bt.itsdevice.domain.dto.FacilityPowerDTO" resultMap="treeMap">
		select t1.facility_no,t1.facility_name,t1.road_no,t1.up_facility_no,t1.sort,t1.mile_post,t1.type,t2.road_name,
		t4.facility_name up_facility_name,t4.facility_type_no up_facility_type_no
		from (
		select t2.facility_no,t2.facility_name,t2.road_no,t2.up_facility_no,t2.mile_post,t1.type,t2.sort
		from facility_power t1,facility t2
		where t1.facility_no=t2.facility_no AND t1.facility_no IN (Facility-Permissions-Check) 
		<if test="facilityName != null &amp;&amp; facilityName != '' "> AND t2.facility_name like CONCAT('%', #{facilityName}, '%') </if>
		<if test="roadNo != null &amp;&amp; roadNo > 0 "> AND t2.road_no=#{roadNo}</if>
		) t1
		left JOIN road t2 on t1.road_no=t2.road_no
		left JOIN facility t4 on t1.up_facility_no=t4.facility_no
		WHERE t4.facility_type_no IS NOT NULL AND t1.road_no IS NOT NULL
		order by t1.sort,t1.mile_post asc	
	</select>
	
	<select id="selectFacilityAll" parameterType="com.bt.itsdevice.domain.dto.FacilityPowerDTO" resultMap="treeMap">
		select facility_no,facility_name,road_no,up_facility_no
		from facility
		where facility_type_no!=44 AND facility_no IN (Facility-Permissions-Check) 
		<if test="roadNo != null &amp;&amp; roadNo > 0 "> AND road_no=#{roadNo}</if>
		ORDER BY mp_value,facility_type_no,sort
	</select>
	
	<select id="getPowerCommons" parameterType="java.lang.String" resultType="com.bt.itscore.domain.vo.FacilityPowerVO">
		select commands,r.source_id sourceId from facility_power p,facility f,road r 
		where p.facility_no=f.facility_no and f.road_no=r.road_no and p.facility_no=#{facilityNo}
	</select>


    <select id="getPageTypeChildren" parameterType="java.lang.String" resultType="com.bt.itsdevice.domain.vo.TreePowerVO">
    	SELECT page_type_code id,t2.`name` text FROM power_page_bg t1,power_page_type t2
		where t1.page_type_code=t2.`code` and t1.facility_no=#{facilityNo}
	</select>

	<insert id="add" parameterType="com.bt.itsdevice.domain.dto.FacilityPowerDTO" useGeneratedKeys="true">
		insert into facility_power(facility_no,type,transformers,install_capacity,report_capacity,commands) 
		values(#{facilityNo},#{type},#{transformers},#{installCapacity},#{reportCapacity},#{commands})
	</insert>
	
	<update id="update" parameterType="com.bt.itsdevice.domain.dto.FacilityPowerDTO">
		update facility_power set  type =#{type},transformers =#{transformers},install_capacity=#{installCapacity},report_capacity=#{reportCapacity}
		where facility_no=#{facilityNo}
	</update>
	
	<delete id="delete" parameterType="com.bt.itsdevice.domain.dto.FacilityPowerDTO">
		delete from facility_power where facility_no=#{facilityNo}
	</delete>

	<delete id="batchDelete" parameterType="com.bt.itsdevice.domain.dto.FacilityPowerDTO">
		delete from facility_power where facility_no IN
		<foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
	</delete>
	
	 <resultMap type="com.bt.itsdevice.domain.vo.FacilityPowerItemVO" id="itemMap">
		<id column="facility_no" property="facilityNo"/>
		<result column="value" property="value"/>
	</resultMap>
	<select id="selectVoltageLevel" parameterType="com.bt.itsdevice.domain.vo.FacilityPowerItemVO" resultType="java.lang.String">
		SELECT `value` FROM power_voltage_level WHERE facility_no=#{facilityNo}	
	</select>
	<select id="selectBackupPower" parameterType="com.bt.itsdevice.domain.vo.FacilityPowerItemVO" resultType="java.lang.String">
		SELECT `value` FROM power_backup_power WHERE facility_no=#{facilityNo}	
	</select>
	<insert id="updateVoltageLevels" parameterType="com.bt.itsdevice.domain.dto.FacilityPowerDTO" >
		INSERT INTO power_voltage_level (facility_no,`value`) VALUES 
		<foreach collection="voltageLevels" item="item" separator=",">
			(
			#{facilityNo},#{item}
			)
		</foreach>
	</insert>
	
    <delete id="deleteVoltageLevels" parameterType="java.lang.String">
		delete from power_voltage_level where facility_no=#{facilityNo}
	</delete>
	
	<insert id="updateBackupPowers" parameterType="com.bt.itsdevice.domain.dto.FacilityPowerDTO" >
		INSERT INTO power_backup_power (facility_no,`value`) VALUES 
		<foreach collection="backupPowers" item="item" separator=",">
			(
			#{facilityNo},#{item}
			)
		</foreach>
	</insert>
	
    <delete id="deleteBackupPowers" parameterType="java.lang.String">
		delete from power_backup_power where facility_no=#{facilityNo}
	</delete>

	<select id="queryPowerCmds" parameterType="string" resultMap="PowerCmdMap">
		SELECT fc.*,f.facility_name
		FROM facility_power_cmd fc
		LEFT JOIN facility f ON fc.facility_no=f.facility_no
		WHERE fc.facility_no=#{facilityNo} AND fc.use=1
	</select>

</mapper>
