package com.bt.itsdevice.service;

import java.util.List;
import java.util.UUID;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.dto.RoadPileNoDTO;
import com.bt.itscore.domain.vo.RoadPileNoVO;
import com.bt.itsdevice.domain.dto.FacilityStationDTO;
import com.bt.itsdevice.domain.vo.FacilityStationVO;
import com.bt.itsdevice.feign.RoadFeignClient;
import com.bt.itsdevice.mapper.FacilityStationMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

@Service("facilityStationService")
public class FacilityStationService {
	@Autowired
    private FacilityService facilityService;

	@Autowired
    private FacilityStationMapper facilityStationMapper;

    @Autowired
    private RoadFeignClient roadFeignClient;


    public PageInfo<FacilityStationVO> page(FacilityStationDTO facilityStationDTO, PageDTO pageDTO ){
        PageHelper.startPage(pageDTO.getPage() , pageDTO.getLimit());
        List<FacilityStationVO> rows = facilityStationMapper.selectList(facilityStationDTO);
        PageInfo<FacilityStationVO> pageInfo = new PageInfo<>(rows);
        return pageInfo;
    }

    public List<FacilityStationVO> selectAll(FacilityStationDTO facilityStationDTO){
    	return facilityStationMapper.selectAll(facilityStationDTO);
    }

    /**
     * @描述 新增收费站，true成功；false失败
     * @param facilityStationDTO
     * @return boolean
     */
    @Transactional
    public boolean add(FacilityStationDTO facilityStationDTO) {
        String milePost = facilityStationDTO.getMilePost();
        // mpValue, 经纬度
        String[] mile = milePost.replace("K", "").split("\\+");
        if (mile.length == 2) {
            facilityStationDTO.setMpValue(NumberUtils.toInt(mile[0]) * 1000 + NumberUtils.toInt(mile[1]));
        } else {
            facilityStationDTO.setMpValue(NumberUtils.toInt(mile[0]) * 1000);
        }
        if (facilityStationDTO.getComputeSwitch() != null && facilityStationDTO.getComputeSwitch() == 1
            && StringUtils.isNotBlank(milePost)) {
            // 桩号转经纬度 表road_pile_no
            RoadPileNoDTO roadPileNoDTO =
                new RoadPileNoDTO(facilityStationDTO.getRoadNo(), facilityStationDTO.getDirectionNo(), milePost);
            RoadPileNoVO roadPileNoVO = roadFeignClient.pileno2Lnglat(roadPileNoDTO);
            if (roadPileNoVO != null) {
                facilityStationDTO.setLng(roadPileNoVO.getLng());
                facilityStationDTO.setLat(roadPileNoVO.getLat());
            }
        }

        facilityStationDTO.setFacilityNo(UUID.randomUUID().toString());
        facilityService.add(facilityStationDTO);
        // facilityStationDTO.setFacilityNo(fa.getFacility_no());
        // roleFacilityService.saveRoleFacility(request,facility);
        return facilityStationMapper.add(facilityStationDTO) > 0;
    }

    /**
     * @描述 修改收费站，true成功；false失败
     * @param facilityStationDTO
     * @return boolean
     */
    @Transactional
    public boolean update(FacilityStationDTO facilityStationDTO) {
        String milePost = facilityStationDTO.getMilePost();
        // mp_value, 经纬度
        String[] mile = facilityStationDTO.getMilePost().replace("K", "").split("\\+");
        if (mile.length == 2) {
            facilityStationDTO.setMpValue(NumberUtils.toInt(mile[0]) * 1000 + NumberUtils.toInt(mile[1]));
        } else {
            facilityStationDTO.setMpValue(NumberUtils.toInt(mile[0]) * 1000);
        }
        if (facilityStationDTO.getComputeSwitch() != null && facilityStationDTO.getComputeSwitch() == 1
            && StringUtils.isNotBlank(milePost)) {
            RoadPileNoDTO roadPileNoDTO = new RoadPileNoDTO(facilityStationDTO.getRoadNo(),
                facilityStationDTO.getDirectionNo(), facilityStationDTO.getMilePost());
            RoadPileNoVO roadPileNoVO = roadFeignClient.pileno2Lnglat(roadPileNoDTO);
            if (roadPileNoVO != null) {
                facilityStationDTO.setLng(roadPileNoVO.getLng());
                facilityStationDTO.setLat(roadPileNoVO.getLat());
            }
        }
        facilityService.update(facilityStationDTO);
        return facilityStationMapper.update(facilityStationDTO) > 0;
    }
    
    public FacilityStationVO selectStationById(IdStringDTO dto) {
        return facilityStationMapper.selectStationById(dto);
    }

}
