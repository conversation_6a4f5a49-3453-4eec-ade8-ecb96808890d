package com.bt.itsdevice.domain.vo;

import com.bt.itscore.domain.vo.FacilityVO;

public class FacilityStationVO extends FacilityVO {
	private String facilityNo;//设施主键-设施no int(11) default null,
	private String roadAlias;//  int(11) default null,
	private Integer facilityTypeNo;//  int(11) default null,
	private String upFacilityNo;//  int(11) default null, 上一级 设施NO （虚拟道路）
	private String upFacilityName;// (虚拟道路)
	private Integer type;// int(11) default null,  '收费站类型 0单侧 1双侧'
	private Integer exportLanes;//  int(11) default null,出口车道数
	private Integer entraLanes;// int(11) default null,入口车道数
	private Integer exportEtcLanes;//  int(11) default null,出口ETC车道数
	private Integer entraEtcLanes;//  int(11) default null,入口ETC车道数
	private String person;//  varchar(50) default null,负责人
	private String phone;//  varchar(50) default null,电话
	private String fax;//  varchar(50) default null,传真
	private String lineCode;// varchar(50) default null, 收费站编码
	private String addressName;// varchar(50) default null, 所在地名
	private String region;      //varchar(255) DEFAULT NULL COMMENT '所在行政区'
	private String address;// varchar(50) default null, 地址
	private String entraNames;// varchar(50) default null, 入口可达道路方向
	private String externalRoad;// varchar(50) default null, 连接外部道路
	private String exportNames;// varchar(500) default null, 出口通达地点
	private String reachScenics;// varchar(500) default null, 出口可达景点、名胜
	private String images; // 收费站图片
	private Integer chargingFlag;//充电站： 0-无 1-有
	
	public String getImages() {
		return images;
	}

	public void setImages(String images) {
		this.images = images;
	}
	@Override
	public String getFacilityNo() {
		return facilityNo;
	}

	@Override
	public void setFacilityNo(String facilityNo) {
		this.facilityNo = facilityNo;
	}

	public String getRoadAlias() {
		return roadAlias;
	}

	public void setRoadAlias(String roadAlias) {
		this.roadAlias = roadAlias;
	}

	@Override
	public Integer getFacilityTypeNo() {
		return facilityTypeNo;
	}

	@Override
	public void setFacilityTypeNo(Integer facilityTypeNo) {
		this.facilityTypeNo = facilityTypeNo;
	}

	@Override
	public String getUpFacilityNo() {
		return upFacilityNo;
	}

	@Override
	public void setUpFacilityNo(String upFacilityNo) {
		this.upFacilityNo = upFacilityNo;
	}

	public String getUpFacilityName() {
		return upFacilityName;
	}

	public void setUpFacilityName(String upFacilityName) {
		this.upFacilityName = upFacilityName;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getExportLanes() {
		return exportLanes;
	}

	public void setExportLanes(Integer exportLanes) {
		this.exportLanes = exportLanes;
	}

	public Integer getEntraLanes() {
		return entraLanes;
	}

	public void setEntraLanes(Integer entraLanes) {
		this.entraLanes = entraLanes;
	}

	public Integer getExportEtcLanes() {
		return exportEtcLanes;
	}

	public void setExportEtcLanes(Integer exportEtcLanes) {
		this.exportEtcLanes = exportEtcLanes;
	}

	public Integer getEntraEtcLanes() {
		return entraEtcLanes;
	}

	public void setEntraEtcLanes(Integer entraEtcLanes) {
		this.entraEtcLanes = entraEtcLanes;
	}

	public String getPerson() {
		return person;
	}

	public void setPerson(String person) {
		this.person = person;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getFax() {
		return fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getAddressName() {
		return addressName;
	}

	public void setAddressName(String addressName) {
		this.addressName = addressName;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getEntraNames() {
		return entraNames;
	}

	public void setEntraNames(String entraNames) {
		this.entraNames = entraNames;
	}

	public String getExternalRoad() {
		return externalRoad;
	}

	public void setExternalRoad(String externalRoad) {
		this.externalRoad = externalRoad;
	}

	public String getExportNames() {
		return exportNames;
	}

	public void setExportNames(String exportNames) {
		this.exportNames = exportNames;
	}

	public String getReachScenics() {
		return reachScenics;
	}

	public void setReachScenics(String reachScenics) {
		this.reachScenics = reachScenics;
	}

	public Integer getChargingFlag() {
		return chargingFlag;
	}

	public void setChargingFlag(Integer chargingFlag) {
		this.chargingFlag = chargingFlag;
	}
	
	
}
