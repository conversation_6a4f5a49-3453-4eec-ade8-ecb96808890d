package com.bt.itsapi.domain.dto;
/**
 * <AUTHOR>
 * @date 2025年4月27日 17:29:17
 * @Description 充电桩实时数据
 */
public class ChargingpileRtDataDTO {
	private String sessionID;//订单号
	private Float chargeCapacity;//已充电量
	private Long openTimestamp;//开始充电时间
	private Long closeTimestamp;//结束充电时间
	private Integer chargeDuration;//已充时长(分钟)
	private Integer remainingChargeTimes;//剩余充电时长（分钟）
	private Float rechargeAmount;//充值金额
	private Float currentBalance;//剩余金额
	private Float deductionAmount;//充电消耗金额
	private Float refundAmount;//退款金额
	private Float standard;//充电单价
	private String deviceName;//设备号
	private String gunName;//枪号
	public String getSessionID() {
		return sessionID;
	}
	public void setSessionID(String sessionID) {
		this.sessionID = sessionID;
	}
	public Float getChargeCapacity() {
		return chargeCapacity;
	}
	public void setChargeCapacity(Float chargeCapacity) {
		this.chargeCapacity = chargeCapacity;
	}
	public Long getOpenTimestamp() {
		return openTimestamp;
	}
	public void setOpenTimestamp(Long openTimestamp) {
		this.openTimestamp = openTimestamp;
	}
	public Long getCloseTimestamp() {
		return closeTimestamp;
	}
	public void setCloseTimestamp(Long closeTimestamp) {
		this.closeTimestamp = closeTimestamp;
	}
	public Integer getChargeDuration() {
		return chargeDuration;
	}
	public void setChargeDuration(Integer chargeDuration) {
		this.chargeDuration = chargeDuration;
	}
	public Integer getRemainingChargeTimes() {
		return remainingChargeTimes;
	}
	public void setRemainingChargeTimes(Integer remainingChargeTimes) {
		this.remainingChargeTimes = remainingChargeTimes;
	}
	public Float getRechargeAmount() {
		return rechargeAmount;
	}
	public void setRechargeAmount(Float rechargeAmount) {
		this.rechargeAmount = rechargeAmount;
	}
	public Float getCurrentBalance() {
		return currentBalance;
	}
	public void setCurrentBalance(Float currentBalance) {
		this.currentBalance = currentBalance;
	}
	public Float getDeductionAmount() {
		return deductionAmount;
	}
	public void setDeductionAmount(Float deductionAmount) {
		this.deductionAmount = deductionAmount;
	}
	public Float getRefundAmount() {
		return refundAmount;
	}
	public void setRefundAmount(Float refundAmount) {
		this.refundAmount = refundAmount;
	}
	public Float getStandard() {
		return standard;
	}
	public void setStandard(Float standard) {
		this.standard = standard;
	}
	public String getDeviceName() {
		return deviceName;
	}
	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}
	public String getGunName() {
		return gunName;
	}
	public void setGunName(String gunName) {
		this.gunName = gunName;
	}
	
}
