package com.bt.itsapi.domain.dto;

public class UavStatusDTO {
	private String dock_sn;// 设备SN编码
	private String status; // 航线执行状态，1-待执行，2-执行中，3-成功，4-取消，5-失败，6-暂停，-1-未知状态
	// PENDING, IN_PROGRESS, SUCCESS, CANCEL, FAILED, PAUSED, UNKNOWN
	private String drone_flight_to_target_status;// 标识无人机飞往目标点过程中的不同状态，0-无飞往目标点任务，1-正在飞往目标点，2-已到达目标点，3-已离开目标点，4-取消飞往目标点，-1-未知状态
	// NO_FLIGHT, FLYING, ARRIVED, LEAVING, CANCELED, UNKNOWN
	private Long time;//查询时间

	public Long getTime() {
		return time;
	}

	public void setTime(Long time) {
		this.time = time;
	}

	public String getDock_sn() {
		return dock_sn;
	}

	public void setDock_sn(String dock_sn) {
		this.dock_sn = dock_sn;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getDrone_flight_to_target_status() {
		return drone_flight_to_target_status;
	}

	public void setDrone_flight_to_target_status(String drone_flight_to_target_status) {
		this.drone_flight_to_target_status = drone_flight_to_target_status;
	}

}
