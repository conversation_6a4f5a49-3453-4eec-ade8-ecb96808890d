<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsapi.mapper.MpAccountMapper">
  <resultMap type="com.bt.itsapi.domain.vo.MpAccountVO" id="mpAccountMap">
    <id column="id" property="id"/>
    <result column="name" property="name"/>
    <result column="app_id" property="appId"/>
    <result column="app_secret" property="appSecret"/>
    <result column="token" property="token"/>
    <result column="aes_key" property="aesKey"/>
    <result column="template_id" property="templateId"/>
    <result column="remark" property="remark"/>

  </resultMap>
  <select id="selectList" parameterType="com.bt.itsapi.domain.vo.MpAccountVO" resultMap="mpAccountMap">
    SELECT * FROM `mp_account` WHERE 1=1
  </select>

</mapper>
