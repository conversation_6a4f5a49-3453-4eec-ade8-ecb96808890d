<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsapi.mapper.OrganizationMapper">
  <resultMap type="com.bt.itsapi.domain.vo.OrganizationVO" id="OrganizationMap">
    <id column="org_id" property="orgId"/>
    <result column="org_name" property="orgName"/>
    <result column="short_name" property="shortName"/>
    <result column="org_type" property="orgType"/>
    <result column="org_tel" property="orgTel"/>
    <result column="leader" property="leader"/>
    <result column="use" property="use"/>
    <result column="pid" property="pid"/>
    <result column="sort" property="sort"/>
    <result column="create_time" property="createTime"/>
  </resultMap>

  <select id="selectHeadquartersById" parameterType="com.bt.itscore.domain.dto.IdStringDTO" resultMap="OrganizationMap">

	SELECT * FROM organization WHERE pid IN (
    SELECT pid FROM organization WHERE org_id=#{id}
  ) AND `org_type`=1 AND `use`=1
  order by sort ASC
  LIMIT 1
	</select>

</mapper>
