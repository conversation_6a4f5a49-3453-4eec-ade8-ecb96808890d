package com.bt.itsapi.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bt.itsapi.domain.dto.OtherServerDTO;
import com.bt.itsapi.service.LoginService;

/**
 * 
 * @Description: 外部数据接口-鉴权相关
 * @author: lsy
 * @date: 2022年9月6日 上午午9:11:27
 */
@RestController
@RequestMapping("login")
public class LoginController {
	@Autowired
	private LoginService loginService;
	
	/**
	 * @api {POST} /login/otherGetToken 外部应用获取token /login/otherGetToken
	 * @apiDescription 外部应用获取token；创建人：lsy，修改人：无
     * @apiGroup  鉴权管理LoginController
     * @apiBody {String} appId=123 外部应用ID
     * @apiBody {String} appSecret=123 外部应用密钥
	 * @apiSuccess (Success 200) {Number} code 0-失败，1-成功
	 * @apiSuccess (Success 200) {String} token 令牌
	 * @apiSuccess (Success 200) {String} message 提示信息
	 * @apiSuccessExample {json} Success-Response:
	 *     HTTP/1.1 200 OK
	 *     {
	 *       "code":1,
	 *       "token":"eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiIxMjMiLCJpYXQiOjE2NjIzNjExNDgsImV4cCI6MTY2MjM3NTU0OH0.D-v3HwUV9a20Z3_6rBJE1wdOc1E8b67aBcweULp3JJ0",
	 *       "message": ""
	 *     }
	 * @apiSampleRequest /login/otherGetToken
	 */
	@PostMapping("/otherGetToken")
	public Object otherGetToken(@RequestBody OtherServerDTO dto) {
		return loginService.otherGetToken(dto);
	}
}
