package com.bt.itsapi.controller;

import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bt.itsapi.domain.dto.ChargingpileDataDTO;
import com.bt.itsapi.domain.dto.ChargingpileStatusDTO;
import com.bt.itsapi.service.IndexService;
import com.bt.itscore.auth.Login;
import com.bt.itscore.domain.dto.AlarmDTO;
import com.bt.itscore.domain.dto.SmsTemplateDTO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.utils.GsonUtils;
/**
 * <AUTHOR>
 * @date 2025年4月27日 17:17:36
 * @Description 第三方接口
 */
@RestController
@RequestMapping("")
public class IndexController {
	private final static Logger LOGGER = LoggerFactory.getLogger(IndexController.class);
	
	@Autowired
	IndexService indexService;

	/**
	 * @Description 充电桩实时数据推送，供信创调用
	 */
	@Login
	@PostMapping("chargingpileRtData")
	public ResponseVO chargingpileRtData(@RequestBody ChargingpileDataDTO dto) {
		LOGGER.info("chargingpileRtData:{}", GsonUtils.beanToJson(dto));
		return new ResponseVO(indexService.addChargingpileRtData(dto));
	}

	/**
	 * @Description 充电桩实时状态推送，供信创调用
	 */
	@Login
	@PostMapping("chargingpileRtStatus")
	public ResponseVO chargingpileRtStatus(@RequestBody ChargingpileStatusDTO dto) {
		LOGGER.info("chargingpileRtStatus:{}", GsonUtils.beanToJson(dto));
		return new ResponseVO(indexService.updateChargingpileRtStatus(dto));
	}

	/**
	 * @Description 发送短信
	 */
	@Login
	@PostMapping("smsSend")
	public ResponseVO smsSend(@RequestBody SmsTemplateDTO dto) {
		LOGGER.info("smsSend dto:{}", GsonUtils.beanToJson(dto));
		return new ResponseVO(indexService.smsSend(dto));
	}

	/**
	 * @Description 边坡预警接收
	 */
	@Login
	@PostMapping("alarmRtData")
	public ResponseVO alarmRtData(@RequestBody AlarmDTO dto) {
		LOGGER.info("alarmRtData dto:{}", GsonUtils.beanToJson(dto));
		if (StringUtils.isBlank(dto.getId())) {
			dto.setId(UUID.randomUUID().toString());
		}
		return indexService.alarmRtData(dto);
	}
	
}
