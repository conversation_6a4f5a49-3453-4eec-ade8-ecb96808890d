-- ----------------------------
-- `user_gzh_yanhai` 增加字段，记录 appId
-- ----------------------------
ALTER TABLE `user_gzh_yanhai` ADD `app_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '公众号 appId';

-- ----------------------------
-- Table structure for mp_account
-- 新建数据表，推送模板消息的微信公众号
-- ----------------------------
DROP TABLE IF EXISTS `mp_account`;
CREATE TABLE `mp_account`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公众号名称',
  `app_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公众号 appId',
  `app_secret` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公众号密钥',
  `token` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '公众号token',
  `aes_key` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '消息加解密密钥',
  `template_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '模板消息的模板id',
  `remark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '微信公众号配置表' ROW_FORMAT = Dynamic;


-- ----------------------------
-- mp_account 测试数据
-- ----------------------------
INSERT INTO `mp_account`(id,`name`,`app_id`,`app_secret`,`token`,`aes_key`,`template_id`,`remark`)
VALUES(NULL,'广西交科智慧高速','wx5b62e01edd2cc42a','4c8cfa1c1f4e36073390a8b88b6d2e42','abc123456','pQFzp5cNDU0yaAX0mLTLEXgyUPGaTpQvenEFBIqZu8U','jNWysHtm2d7mqXNzkPO1Klg3lq0pvDJyunXNoKz4Zqk','');

INSERT INTO `mp_account`(id,`name`,`app_id`,`app_secret`,`token`,`aes_key`,`template_id`,`remark`)
VALUES(NULL,'广西沿海高速小螺号','wx6282a97f1f474424','f7a5c2afd646d9f5fea341b2b517e506','abc123456','pQFzp5cNDU0yaAX0mLTLEXgyUPGaTpQvenEFBIqZu8U','zPohXIgHpDuldBk3Sj7li2MgX6hKSicBNdBjaEgu79U','');


