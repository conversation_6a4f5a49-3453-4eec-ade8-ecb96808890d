package com.bt.itsms.mq.rabbitmq;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023年2月21日 上午9:38:23
 * @Description 新发展集团的队列消息
 */
@Component
public class XfzCommandProductor {
	private static final Logger LOGGER = LoggerFactory.getLogger(XfzCommandProductor.class);

	private MessageChannel output;

	public XfzCommandProductor(@Qualifier(XfzCommandSource.COMMAND) MessageChannel xfzCommandOutput) {
		this.output = xfzCommandOutput;
	}

	// 生产消息
	public String produce(String msg) {
		boolean result = this.output.send(MessageBuilder.withPayload(msg).build());
		LOGGER.info("xfzCommand消息发送结果:{}", result);
		return "sendTo..." + msg;
	}

}
