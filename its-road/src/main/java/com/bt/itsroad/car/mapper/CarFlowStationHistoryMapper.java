package com.bt.itsroad.car.mapper;

import com.bt.itsroad.car.domain.dto.CarFlowStationHistoryDTO;
import com.bt.itsroad.car.domain.vo.CarFlowStationHistoryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface CarFlowStationHistoryMapper {
    int insertStationHistoryList(List<CarFlowStationHistoryDTO> list); // 批量插入数据
    CarFlowStationHistoryVO getHistoryCarFlowByStationId(@Param("stationId") String stationId
            , @Param("startTime") Date start, @Param("endTime") Date end); // 根据收费站编号、时间段查询通行数据（不区分方向，两个方向的汇总）
    CarFlowStationHistoryVO getHistoryCarFlowByStationIdAndDirection(@Param("stationId") String stationId, @Param("direction") int direction
            , @Param("startTime") Date start, @Param("endTime") Date end); // 根据收费站编号、时间段查询通行数据（区分方向）
}
