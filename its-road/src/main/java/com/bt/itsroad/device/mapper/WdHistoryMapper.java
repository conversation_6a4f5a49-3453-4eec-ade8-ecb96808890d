package com.bt.itsroad.device.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.bt.itsroad.device.domain.dto.DeviceDTO;
import com.bt.itsroad.device.domain.vo.WdHistoryVO;

@Mapper
public interface WdHistoryMapper {

	List<WdHistoryVO> selectWdToday(DeviceDTO deviceDTO);

	WdHistoryVO selectWdNow(DeviceDTO deviceDTO);

	List<WdHistoryVO> selectWdMonth(DeviceDTO deviceDTO);

}
