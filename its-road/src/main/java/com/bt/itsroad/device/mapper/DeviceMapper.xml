<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsroad.device.mapper.DeviceMapper">
	<resultMap type="com.bt.itsroad.device.domain.vo.DeviceVO" id="deviceMap">
        <result property="deviceId" column="device_id"/>
        <result property="facilityNo" column="facility_no"/>
        <result property="directionNo" column="direction_no"/>
        <result property="deviceName" column="device_name"/>
        <result property="milePost" column="mile_post"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="roadNo" column="road_no"/>
        <result property="directionName" column="direction_name"/>
        <result property="roadName" column="road_name"/>
        <result property="facilityName" column="facility_name"/>
        <result property="roadShortName" column="road_short_name"/>
        <result property="deviceShortName" column="device_short_name"/>
        <result property="sourceId" column="source_id"/>
    </resultMap>
	
	<select id="selectByLaneId" resultMap="deviceMap" parameterType="com.bt.itsroad.device.domain.dto.DeviceDTO">
    	select d.*,f.facility_name,di.direction_name direction_name,f.road_no,r.road_name,r.road_alias AS road_short_name from device_camera dc
    	left join device d on dc.device_id=d.device_id
        left join direction di on d.direction_no = di.direction_no
        left join facility f on f.facility_no = d.facility_no
        left join road r on f.road_no = r.road_no where dc.lane_id =#{laneId} limit 1
    </select>
    
	<select id="selectDeviceById" resultMap="deviceMap" parameterType="com.bt.itsroad.device.domain.dto.DeviceDTO">
    	select d.*,f.facility_name,di.direction_name direction_name,f.road_no,r.road_name,r.road_alias AS road_short_name from device d
        left join direction di on d.direction_no = di.direction_no
        left join facility f on f.facility_no = d.facility_no
        left join road r on f.road_no = r.road_no where (d.old_device_id IS NOT NULL AND CAST(d.old_device_id AS CHAR) =#{deviceId} and d.source_id=#{sourceId}) or d.device_id=#{deviceId} limit 1
    </select>

    <select id="selectByCameraCode" parameterType="string" resultType="string">
        SELECT device_id FROM device_camera WHERE camera_code=#{cameraCode} LIMIT 1
    </select>

    <select id="selectDeivceByDeviceId" parameterType="string" resultMap="deviceMap">
        SELECT * FROM device WHERE device_id=#{deviceId} OR old_device_id=#{deviceId} LIMIT 1
    </select>

    <select id="selectSlopeByDeviceId" parameterType="string" resultMap="deviceMap">
    SELECT f.road_no, f.facility_name, f.mile_post, f.lng, f.lat, r.road_name,r.road_alias AS road_short_name,di.direction_no,di.direction_name FROM facility_slope_r s, facility f
    LEFT JOIN road r ON f.road_no = r.road_no 
    left join direction di on f.direction_no = di.direction_no
    WHERE s.facility_no=f.facility_no AND s.device_id=#{deviceId} LIMIT 1
    </select>

    <select id="selectFacilityById" parameterType="string" resultType="com.bt.itscore.domain.vo.FacilityVO">
        SELECT f.facility_name AS facilityName,
        f.facility_no AS facilityNo,
        f.mile_post AS milePost,
        f.lng,
        f.lat,
        f.road_no AS roadNo,
        r.road_name AS roadName,
        r.road_alias AS roadAlias
        FROM facility f  LEFT JOIN road r ON r.road_no=f.road_no WHERE f.facility_no=#{facilityNo} LIMIT 1
    </select>

    <resultMap id="OrgRoadListMap" type="com.bt.itsroad.device.domain.vo.OrgRoadListVO">
        <id column="org_id" property="orgId"/>
        <result column="org_name" property="orgName"/>
        <collection property="children" ofType="com.bt.itsroad.feign.RoadVO">
            <id column="road_no" property="roadNo"/>
            <result column="road_name" property="roadName"/>
            <result column="road_alias" property="roadAlias"/>
            <result column="parent_road" property="parentRoad"/>
            <collection property="facilitys" ofType="com.bt.itsroad.feign.FacilityVO">
                <id column="facility_no" property="facilityNo"/>
                <result column="facility_name" property="facilityName"/>
            </collection>
        </collection>
    </resultMap>
    <select id="orgServiceList" resultMap="OrgRoadListMap" parameterType="java.util.Map">
        SELECT o.org_id,org.org_name,r.road_no,r.road_name,r.road_alias,r.parent_road,f.facility_no,f.facility_name
        FROM road r, facility f, device d, device_camera dc, organization_road o
        LEFT JOIN organization org ON o.org_id=org.org_id
        WHERE o.road_no=r.road_no AND f.road_no = r.road_no AND f.facility_type_no = 7
        AND f.facility_no IN (SELECT DISTINCT facility_no FROM role_facility rf WHERE rf.role_id IN
        <foreach collection="roles" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
        )
        AND d.facility_no = f.facility_no AND d.device_id = dc.device_id AND ( dc.entry_flag=1 OR dc.entry_flag=2 )
    </select>
    <select id="orgAllServiceList" resultMap="OrgRoadListMap" parameterType="java.util.Map">
        SELECT o.org_id,org.org_name,r.road_no,r.road_name,r.road_alias,r.parent_road,f.facility_no,f.facility_name
        FROM road r, facility f, device d, device_camera dc, organization_road o
        LEFT JOIN organization org ON o.org_id=org.org_id
        WHERE o.road_no=r.road_no AND f.road_no = r.road_no AND f.facility_type_no = 7
        AND f.facility_no IN (SELECT DISTINCT facility_no FROM role_facility rf WHERE rf.role_id IN
        <foreach collection="roles" item="roleId" separator="," open="(" close=")">#{roleId}</foreach>
        )
        AND d.facility_no = f.facility_no AND d.device_id = dc.device_id
    </select>

</mapper>