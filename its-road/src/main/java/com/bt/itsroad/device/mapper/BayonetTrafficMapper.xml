<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsroad.device.mapper.BayonetTrafficMapper">
	<resultMap type="com.bt.itsroad.device.domain.vo.BayonetTrafficVO" id="BayonetTrafficMap">
		<id column="id" property="id"/>
		<result column="facility_no" property="facilityNo"/>
		<result column="flag" property="flag"/>
		<result column="in_big_num" property="bigNum"/>
		<result column="in_small_num" property="smallNum"/>
		<result column="in_total_num" property="totalNum"/>
		<result column="time" property="time"/>
		<!-- 一对多的关系 -->  
        <!-- property: 指的是集合属性的值, ofType：指的是集合中元素的类型 -->
        <!-- <collection property="cameraList" ofType="com.bt.itsroad.device.domain.vo.CameraVO"> 
			<result column="device_id" property="deviceId"/>
			<result column="device_name" property="deviceName"/>
			<result column="camera_code" property="cameraCode"/>
		</collection> -->
	</resultMap>
	<select id="selectBayonetTraffic" parameterType="com.bt.itsroad.device.domain.dto.FacilityDTO" resultMap="BayonetTrafficMap">
<!-- 		select fs.facility_no,bt.facility_no bayonet_no,sum(in_big_num) in_big_num,sum(in_small_num) in_small_num,sum(in_total_num) in_total_num  -->
<!-- 		from facility_sa fs,bayonet_traffic bt where FIND_IN_SET(bt.facility_no,fs.bayonet_id) and bt.flag=#{flag} and fs.facility_no=#{facilityNo} and TO_DAYS(time) = TO_DAYS(NOW()) -->
		select fs.facility_no,bt.facility_no bayonet_no,sum(in_big_num) in_big_num,sum(in_small_num) in_small_num,sum(in_total_num) in_total_num 
		from facility_sa fs,bayonet_traffic bt where bt.facility_no=#{facilityNo} and bt.flag=#{flag} and fs.facility_no=#{facilityNo} and TO_DAYS(time) = TO_DAYS(NOW())
	</select>
</mapper>