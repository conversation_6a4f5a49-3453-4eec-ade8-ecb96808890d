package com.bt.itsroad.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix = "etc")
@Component
public class EtcMQConfig {
	 private boolean flag;
	 private String queue;

	public boolean isFlag() {
		return flag;
	}
	public void setFlag(boolean flag) {
		this.flag = flag;
	}
	
	public String getQueue() {
		return queue;
	}
	public void setQueue(String queue) {
		this.queue = queue;
	}

	@Override
	public String toString() {
		return "EtcMQConfig [flag=" + flag + ", queue=" + queue + "]";
	}
}
