package com.bt.itsinnerjob.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.bt.itsinnerjob.domain.dto.AlarmDTO;

@Mapper
public interface FailAlarmMapper {

    int add(AlarmDTO dto);

    int delete(AlarmDTO dto);

    List<AlarmDTO> selectUploadFail(int value);
    
    int updateSuccessUploadAlarmStatus(String alarmId);
    
    int updateFailUploadAlarmStatus(String alarmId);
    
    int updateSuccessUploadImgStatus(String alarmId);
    
    int updateFailUploadImgStatus(String alarmId);

    int updateSuccessUploadVideoStatus(String alarmId);
    
    int updateFailUploadVideoStatus(String alarmId);

    int updateVideoId(Map<String, Object> map);

    List<AlarmDTO> selectUploadImgFail(int value);

    List<AlarmDTO> selectUploadVideoFail(int value);
    
    List<AlarmDTO> selectUploadVideoFailByFactory(int value);
}
