<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatif.org//DTD Mapper 3.0//EN" "http://mybatif.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsinnerjob.mapper.detector.DeviceVdMapper">

    <select id="getbyip" resultType="com.bt.itsinnerjob.detector.entity.DeviceVd" parameterType="java.lang.String">
        select
        f.facility_no,f.facility_name,f.facility_type_no,f.road_no,dd.device_id,dd.device_name,dd.mile_post,dd.mp_value,dd.lng,dd.lat,dd.vd_type,dd.protocol,dd.version,dd.ip_address,dd.port,dd.com_port,dd.protocol_flag,dd.lane_num,dd.use,dd.vd_model,e.direction_no,e.direction_name,dd.status
        from
        ( select
        d.device_id,d.device_name,d.mile_post,d.mp_value,d.lng,d.lat,d.facility_no,d.device_type_no,d.direction_no,c.vd_type,c.protocol,c.version,c.ip_address,c.port,c.com_port,c.protocol_flag,c.lane_num,c.vd_model,d.use,d.status
        from device d, device_vd c where d.device_type_no=1 and d.device_id=c.device_id and d.device_id in
        ( select device_id from device_vd where ip_address = #{ip} )
        ) dd left join direction e on dd.direction_no=e.direction_no left
        left join facility f on dd.facility_no=f.facility_no
        order by dd.device_id desc
    </select>

    <select id="listByProtocol" resultType="com.bt.itsinnerjob.detector.entity.DeviceVd" parameterType="com.bt.itsinnerjob.detector.entity.DeviceVd">
        select
        f.facility_no,f.facility_name,f.facility_type_no,f.road_no,dd.device_id,dd.device_name,dd.mile_post,dd.mp_value,dd.mp_value,dd.lng,dd.lat,dd.vd_type,dd.protocol,dd.version,dd.ip_address,dd.port,dd.com_port,dd.protocol_flag,dd.lane_num,dd.use,dd.vd_model,e.direction_no,e.direction_name,dd.status
        from
        ( select
        d.device_id,d.device_name,d.mile_post,d.mp_value,d.lng,d.lat,d.facility_no,d.device_type_no,d.direction_no,c.vd_type,c.protocol,c.version,c.ip_address,c.port,c.com_port,c.protocol_flag,c.lane_num,c.vd_model,d.use,d.status
        from device d, device_vd c where d.device_type_no=1 and d.device_id=c.device_id and d.device_id in (
        select device_id from device_vd where protocol = #{protocol} and d.use = 1
        <if test="source_id != null and source_id != ''">and d.source_id=#{source_id}</if>
        )
        ) dd left join direction e on dd.direction_no=e.direction_no
        left join facility f on dd.facility_no=f.facility_no
        order by dd.device_id desc
    </select>

    <select id="queryByDeviceid" parameterType="java.lang.String" resultType="com.bt.itsinnerjob.detector.entity.Device">
        select dd.device_name,dd.mile_post,dd.direction_no,di.direction_name,dd.lng,dd.lat,dd.use,r.road_no,r.road_alias,r.road_name from
        (select d.device_name,d.mile_post,d.direction_no,d.lng,d.lat,d.use from device d where d.device_id =#{_parameter} ) dd
        left join direction di on dd.direction_no=di.direction_no
        left join road r on di.road_no=r.road_no
    </select>

    <select id="list" resultType="com.bt.itsinnerjob.detector.entity.DeviceVd" parameterType="com.bt.itsinnerjob.detector.entity.DeviceVd">
        select
        f.facility_no,f.facility_name,f.facility_type_no,f.road_no,dd.device_id,dd.device_name,dd.mile_post,dd.mp_value,dd.lng,dd.lat,dd.vd_type,dd.protocol,dd.version,dd.ip_address,dd.port,dd.com_port,dd.protocol_flag,dd.lane_num,dd.use,dd.vd_model,e.direction_no,e.direction_name,dd.status
        from
        ( select
        d.device_id,d.device_name,d.mile_post,d.mp_value,d.lng,d.lat,d.facility_no,d.device_type_no,d.direction_no,c.vd_type,c.protocol,c.version,c.ip_address,c.port,c.com_port,c.protocol_flag,c.lane_num,c.vd_model,d.use,d.status
        from device d, device_vd c where d.device_type_no=1 and d.device_id=c.device_id and d.use = 1
        <if test="source_id != null and source_id != ''">and d.source_id=#{source_id}</if>
        ) dd left join direction e on dd.direction_no=e.direction_no
        left join facility f on dd.facility_no=f.facility_no
    </select>

</mapper>