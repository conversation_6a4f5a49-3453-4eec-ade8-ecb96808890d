package com.bt.itsinnerjob.electric.domain.bean;

/**
 * 描述：火警设备信息
 *
 * <AUTHOR>
 * @since 2023-12-25 14:44
 */
public class DeviceElectricBean {
    private String deviceId;
    private String facilityNo;
    private String deviceName;
    private Integer cirNo;
    private Integer addressNo;
    private Integer builderNo;
    private Integer areaNo;
    private Integer layerNo;
    private Integer number;
    private Long createTime;
    private Long updateTime;
    private Integer delete;

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getFacilityNo() {
        return facilityNo;
    }

    public void setFacilityNo(String facilityNo) {
        this.facilityNo = facilityNo;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public Integer getCirNo() {
        return cirNo;
    }

    public void setCirNo(Integer cirNo) {
        this.cirNo = cirNo;
    }

    public Integer getAddressNo() {
        return addressNo;
    }

    public void setAddressNo(Integer addressNo) {
        this.addressNo = addressNo;
    }

    public Integer getBuilderNo() {
        return builderNo;
    }

    public void setBuilderNo(Integer builderNo) {
        this.builderNo = builderNo;
    }

    public Integer getAreaNo() {
        return areaNo;
    }

    public void setAreaNo(Integer areaNo) {
        this.areaNo = areaNo;
    }

    public Integer getLayerNo() {
        return layerNo;
    }

    public void setLayerNo(Integer layerNo) {
        this.layerNo = layerNo;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDelete() {
        return delete;
    }

    public void setDelete(Integer delete) {
        this.delete = delete;
    }
}
