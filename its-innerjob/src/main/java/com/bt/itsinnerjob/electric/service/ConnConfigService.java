package com.bt.itsinnerjob.electric.service;

import com.bt.itsinnerjob.electric.domain.bean.ConnConfigBean;
import com.bt.itsinnerjob.mapper.electric.ConnConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 描述：连接配置的服务类
 *
 * <AUTHOR>
 * @since 2023-12-05 09:09
 */
@Service
public class ConnConfigService {

    @Autowired
    private ConnConfigMapper connConfigMapper;

    public List<ConnConfigBean> queryAllConnConfigList() {
        return connConfigMapper.queryAllConnConfigList();
    }

}
