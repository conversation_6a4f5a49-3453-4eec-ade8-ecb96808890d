package com.bt.itsinnerjob.electric.service;

import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.utils.HttpClientUtils;
import com.bt.itscore.utils.ServiceUtils;
import com.bt.itsinnerjob.domain.dto.AlarmDTO;
import com.bt.itsinnerjob.mapper.electric.ElectricAlarmMapper;
import com.google.gson.Gson;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 描述：告警业务类
 *
 * <AUTHOR>
 * @since 2023-12-25 16:31
 */
@Service
public class ElectricAlarmService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ElectricAlarmService.class);

    private static final long THIRTY_DAYS_MILLION_TIME = 30 * 24 * 60 * 60; // 单位秒
    private static final int DELETE_MAX_SIZE = 100; // 删除的最大条数
    @Value("${yk.domain:https://yktest.gxits.cn:8763/s}")
    private String ykDomain;

    @Autowired
    private ElectricAlarmMapper electricAlarmMapper;

    public boolean addAlarm(AlarmDTO alarmDTO) {
        return electricAlarmMapper.add(alarmDTO) > 0;
    }

    public boolean uploadToWeb(AlarmDTO alarmDTO) {
        Map<String, String> headParam = ServiceUtils.getInnerLoginHead();
        String jsonData = new Gson().toJson(alarmDTO);
        String result = HttpClientUtils.post(ykDomain + "/its-ms/ms/alarmAdd", headParam, jsonData);
        ResponseVO res = new Gson().fromJson(result, ResponseVO.class);
        int code = res == null ? 0 : res.getCode();
        return code > 0;
    }

    public void updateAlarm(AlarmDTO dto) {
        electricAlarmMapper.update(dto);
    }

    public void rePushAlarm() {
        // 删除已上传成功且超过两天的数据
        long createTime = System.currentTimeMillis() / 1000 - THIRTY_DAYS_MILLION_TIME;
        List<AlarmDTO> alarmDTOList = electricAlarmMapper.querySuccessAlarm(createTime);
        if (!CollectionUtils.isEmpty(alarmDTOList)) {
            int size = alarmDTOList.size();
            int circle = size / DELETE_MAX_SIZE;
            int remain = size % DELETE_MAX_SIZE;
            for (int i = 0; i < circle; i++) {
                electricAlarmMapper.batchDelete(alarmDTOList.subList(i * DELETE_MAX_SIZE, (i + 1) * DELETE_MAX_SIZE));
            }
            if (remain > 0) {
                electricAlarmMapper.batchDelete(alarmDTOList.subList(circle * DELETE_MAX_SIZE, size));
            }
        }
        // 查询出失败的数据，进行重新推送
        List<AlarmDTO> failedAlarmList = electricAlarmMapper.queryFailedAlarm();
        if (CollectionUtils.isEmpty(failedAlarmList)) {
            XxlJobLogger.log("电气火灾无失败数据，无需重新推送");
            return;
        }
        for (AlarmDTO alarmDTO : failedAlarmList) {
            boolean isSuccess = uploadToWeb(alarmDTO);
            if (isSuccess){
                alarmDTO.setUploadAlarmStatus(1);
                updateAlarm(alarmDTO);
            }
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                XxlJobLogger.log("休眠线程被中断" + e);
            }
        }
    }
}
