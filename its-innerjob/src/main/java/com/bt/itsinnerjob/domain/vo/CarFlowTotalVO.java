package com.bt.itsinnerjob.domain.vo;

public class CarFlowTotalVO {
	private String id;// ID
	private Integer eToday;// 入口今日新发展管辖路段车流总量
	private Integer eYesterday;// 入口昨日新发展管辖路段车流总量
	private Integer eTodayLastYear; // 入口去年今日新发展管辖路段车流量
	private Integer xToday;// 出口今日新发展管辖路段车流总量
	private Integer xYesterday;// 出口昨日新发展管辖路段车流总量
	private Integer xTodayLastYear; // 出口去年今日新发展管辖路段车流量
	private String createTime; // 数据统计时间
	private String uploadStatus; // 上传状态（1-成功 2-失败 默认2）

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Integer geteToday() {
		return eToday;
	}

	public void seteToday(Integer eToday) {
		this.eToday = eToday;
	}

	public Integer geteYesterday() {
		return eYesterday;
	}

	public void seteYesterday(Integer eYesterday) {
		this.eYesterday = eYesterday;
	}

	public Integer geteTodayLastYear() {
		return eTodayLastYear;
	}

	public void seteTodayLastYear(Integer eTodayLastYear) {
		this.eTodayLastYear = eTodayLastYear;
	}

	public Integer getxToday() {
		return xToday;
	}

	public void setxToday(Integer xToday) {
		this.xToday = xToday;
	}

	public Integer getxYesterday() {
		return xYesterday;
	}

	public void setxYesterday(Integer xYesterday) {
		this.xYesterday = xYesterday;
	}

	public Integer getxTodayLastYear() {
		return xTodayLastYear;
	}

	public void setxTodayLastYear(Integer xTodayLastYear) {
		this.xTodayLastYear = xTodayLastYear;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getUploadStatus() {
		return uploadStatus;
	}

	public void setUploadStatus(String uploadStatus) {
		this.uploadStatus = uploadStatus;
	}

	

}
