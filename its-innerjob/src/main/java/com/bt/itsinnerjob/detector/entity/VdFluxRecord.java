package com.bt.itsinnerjob.detector.entity;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class VdFluxRecord {
    private List<String> idList;
    private String id;
    private String device_id;
    private String vd_name;
    private String vd_direction;
    private String stat_title;
    private Integer stat_title_id;
    private double road1;
    private double road2;
    private double road3;
    private double road4;
    private double road5;
    private double road6;
    private double road7;
    private double road8;
    private String vd_type;
    private String write_date;
    private Integer date_stamp;


    public List<String> getIdList() {
        return idList;
    }

    public void setIdList(List<String> idList) {
        this.idList = idList;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setStat_title_id(Integer stat_title_id) {
        this.stat_title_id = stat_title_id;
    }

    public void setDate_stamp(Integer date_stamp) {
        this.date_stamp = date_stamp;
    }

    public String getDevice_id() {
        return device_id;
    }

    public void setDevice_id(String device_id) {
        this.device_id = device_id;
    }

    public String getVd_name() {
        return vd_name;
    }

    public void setVd_name(String vd_name) {
        this.vd_name = vd_name;
    }

    public String getVd_direction() {
        return vd_direction;
    }

    public void setVd_direction(String vd_direction) {
        this.vd_direction = vd_direction;
    }

    public String getStat_title() {
        return stat_title;
    }

    public void setStat_title(String stat_title) {
        this.stat_title = stat_title;
    }

    public Integer getStat_title_id() {
        return stat_title_id;
    }

    public void setStat_title_id(int stat_title_id) {
        this.stat_title_id = stat_title_id;
    }

    public double getRoad1() {
        return road1;
    }

    public void setRoad1(double road1) {
        this.road1 = road1;
    }

    public double getRoad2() {
        return road2;
    }

    public void setRoad2(double road2) {
        this.road2 = road2;
    }

    public double getRoad3() {
        return road3;
    }

    public void setRoad3(double road3) {
        this.road3 = road3;
    }

    public double getRoad4() {
        return road4;
    }

    public void setRoad4(double road4) {
        this.road4 = road4;
    }

    public double getRoad5() {
        return road5;
    }

    public void setRoad5(double road5) {
        this.road5 = road5;
    }

    public double getRoad6() {
        return road6;
    }

    public void setRoad6(double road6) {
        this.road6 = road6;
    }

    public double getRoad7() {
        return road7;
    }

    public void setRoad7(double road7) {
        this.road7 = road7;
    }

    public double getRoad8() {
        return road8;
    }

    public void setRoad8(double road8) {
        this.road8 = road8;
    }

    public String getVd_type() {
        return vd_type;
    }

    public void setVd_type(String vd_type) {
        this.vd_type = vd_type;
    }

    public String getWrite_date() {
        return write_date;
    }

    public void setWrite_date(String write_date) {
        this.write_date = write_date;
    }

    public Integer getDate_stamp() {
        return date_stamp;
    }

    public void setDate_stamp(int date_stamp) {
        this.date_stamp = date_stamp;
    }

    public void batchSetRoad(int roadNumber, double roadContent) {
        switch (roadNumber) {
            case 1:
                setRoad1(roadContent);
                break;
            case 2:
                setRoad2(roadContent);
                break;
            case 3:
                setRoad3(roadContent);
                break;
            case 4:
                setRoad4(roadContent);
                break;
            case 5:
                setRoad5(roadContent);
                break;
            case 6:
                setRoad6(roadContent);
                break;
            case 7:
                setRoad7(roadContent);
                break;
            case 8:
                setRoad8(roadContent);
                break;
        }
    }

    @Override
    public int hashCode() {
        return Objects.hash(idList, id, device_id, vd_name, vd_direction, stat_title, stat_title_id, road1, road2, road3, road4, road5, road6, road7, road8, vd_type, write_date, date_stamp);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        final VdFluxRecord other = (VdFluxRecord) obj;
        String selfCode = (this.device_id == null ? "" : this.device_id + "")
                + (this.getDate_stamp() == null ? "" : this.getDate_stamp())
                + this.getStat_title_id();
        String otherCode = (other.getDevice_id() == null ? "" : other.getDevice_id() + "")
                + (this.getDate_stamp() == null ? "" : this.getDate_stamp())
                + this.getStat_title_id();
        boolean flag = selfCode.equals(otherCode);
        return flag;
    }

    public static VdFlux convertToVdFlux(List<VdFluxRecord> list) {
        VdFlux flux = new VdFlux();
        int[] flows = new int[8];
        int totalFlow = 0;
        for (int i = 0; i < list.size(); i++) {
            VdFluxRecord record = list.get(i);
            if (i == 0) {
                flux.setDevice_id(record.getDevice_id());
                flux.setVd_name(record.getVd_name());
                flux.setVd_type(record.getVd_type());
                flux.setWrite_date(record.getWrite_date());
                flux.setDate_stamp(record.getDate_stamp());
                flux.setVd_direction(0);
            }
            if (record.getStat_title_id() == 1) {
                flows[0] = (int) record.getRoad1();
                flows[1] = (int) record.getRoad2();
                flows[2] = (int) record.getRoad3();
                flows[3] = (int) record.getRoad4();
                flows[4] = (int) record.getRoad5();
                flows[5] = (int) record.getRoad6();
                flows[6] = (int) record.getRoad7();
                flows[7] = (int) record.getRoad8();
                totalFlow = flows[0] + flows[1] + flows[2] + flows[3] + flows[4] + flows[5] + flows[6] + flows[7];
                flux.setFlow(totalFlow);
            }
        }
        for (VdFluxRecord record : list) {
            double temp = record.getRoad1() * flows[0] + record.getRoad2() * flows[1] + record.getRoad3() * flows[2] +
                    record.getRoad4() * flows[3] + record.getRoad5() * flows[4] + record.getRoad6() * flows[5] +
                    record.getRoad7() * flows[6] + record.getRoad8() * flows[7];
            switch (record.getStat_title_id()) {
                case 2:
                    flux.setSpeed(totalFlow==0?0:temp / (double) totalFlow);
                    break;
                case 3:
                    flux.setCarfollowing(totalFlow==0?0:temp / (double) totalFlow);
                    break;
                case 4:
                    flux.setSpacing(totalFlow==0?0:(int) (temp / totalFlow));
                    break;
                case 5:
                    flux.setOccupancy(totalFlow==0?0:temp / (double) totalFlow);
                    break;
            }
        }
        return flux;
    }

    public static List<VdFlux> convertToTvsFlux(List<VdFluxRecord> list, int lane) {
        VdFlux upFlux = new VdFlux();
        VdFlux downFlux = new VdFlux();
        List<VdFlux> result = new ArrayList<>();
        result.add(upFlux);
        result.add(downFlux);
        int[] upflows = new int[4];
        int[] downflows = new int[4];
        int upFlow = 0;
        int downFlow = 0;
        for (int i = 0; i < list.size(); i++) {
            VdFluxRecord record = list.get(i);
            if (lane == 4) {
                record.setRoad5(record.getRoad3());
                record.setRoad6(record.getRoad4());
                record.setRoad3(0);
                record.setRoad4(0);
            } else if (lane == 6) {
                record.setRoad7(record.getRoad6());
                record.setRoad6(record.getRoad5());
                record.setRoad5(record.getRoad4());
                record.setRoad4(0);
            }
            if (i == 0) {
                upFlux.setDevice_id(record.getDevice_id());
                upFlux.setVd_name(record.getVd_name());
                upFlux.setVd_type(record.getVd_type());
                upFlux.setWrite_date(record.getWrite_date());
                upFlux.setDate_stamp(record.getDate_stamp());
                upFlux.setVd_direction(1);

                downFlux.setDevice_id(record.getDevice_id());
                downFlux.setVd_name(record.getVd_name());
                downFlux.setVd_type(record.getVd_type());
                downFlux.setWrite_date(record.getWrite_date());
                downFlux.setDate_stamp(record.getDate_stamp());
                downFlux.setVd_direction(2);
            }
            if (record.getStat_title_id() == 1) {
                upflows[0] = (int) record.getRoad1();
                upflows[1] = (int) record.getRoad2();
                upflows[2] = (int) record.getRoad3();
                upflows[3] = (int) record.getRoad4();
                downflows[0] = (int) record.getRoad5();
                downflows[1] = (int) record.getRoad6();
                downflows[2] = (int) record.getRoad7();
                downflows[3] = (int) record.getRoad8();
                
                upFlow = upflows[0] + upflows[1] + upflows[2] + upflows[3];
                upFlux.setFlow(upFlow);
                downFlow = downflows[0] + downflows[1] + downflows[2] + downflows[3];
                downFlux.setFlow(downFlow);
            }
        }
        for (VdFluxRecord record : list) {
            double temp1 = record.getRoad1() * upflows[0] + record.getRoad2() * upflows[1] + record.getRoad3() * upflows[2] +
                    record.getRoad4() * upflows[3];
            double temp2 = record.getRoad5() * downflows[0] + record.getRoad6() * downflows[1] +
                    record.getRoad7() * downflows[2] + record.getRoad8() * downflows[3];
            switch (record.getStat_title_id()) {
                case 2:
                    upFlux.setSpeed(temp1 / (double) upFlow);
                    downFlux.setSpeed(temp2 / (double) downFlow);
                    break;
                case 3:
                    upFlux.setCarfollowing(temp1 / (double) upFlow);
                    downFlux.setCarfollowing(temp2 / (double) downFlow);
                    break;
                case 4:
                    upFlux.setSpacing((int) (temp1 / upFlow));
                    downFlux.setSpacing((int) (temp2 / (double) downFlow));
                    break;
                case 5:
                    upFlux.setOccupancy(temp1 / (double) upFlow);
                    downFlux.setOccupancy(temp2 / (double) downFlow);
                    break;
            }
        }
        return result;
    }
}
