package com.bt.itsinnerjob.detector.entity;

import java.io.Serializable;
import java.util.List;

public class VdFlux implements Serializable {
	private static final long serialVersionUID = 1L;
	private String id;
    private List<String> idList;
    private String device_id;
    private String vd_name;
    private Integer vd_direction;
    private Integer flow;
    private Double speed;
    private Double carfollowing;
    private Integer spacing;
    private Double occupancy;
    private Integer carlength;
    private String vd_type;
    private String write_date;
    private Integer date_stamp;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<String> getIdList() {
        return idList;
    }

    public void setIdList(List<String> idList) {
        this.idList = idList;
    }

    public String getDevice_id() {
        return device_id;
    }

    public void setDevice_id(String device_id) {
        this.device_id = device_id;
    }

    public String getVd_name() {
        return vd_name;
    }

    public void setVd_name(String vd_name) {
        this.vd_name = vd_name;
    }

    public Integer getVd_direction() {
        return vd_direction;
    }

    public void setVd_direction(Integer vd_direction) {
        this.vd_direction = vd_direction;
    }

    public Integer getFlow() {
        return flow;
    }

    public void setFlow(Integer flow) {
        this.flow = flow;
    }

    public Double getSpeed() {
        return speed;
    }

    public void setSpeed(Double speed) {
        this.speed = speed;
    }

    public Double getCarfollowing() {
        return carfollowing;
    }

    public void setCarfollowing(Double carfollowing) {
        this.carfollowing = carfollowing;
    }

    public Integer getSpacing() {
        return spacing;
    }

    public void setSpacing(Integer spacing) {
        this.spacing = spacing;
    }

    public Double getOccupancy() {
        return occupancy;
    }

    public void setOccupancy(Double occupancy) {
        this.occupancy = occupancy;
    }

    public Integer getCarlength() {
        return carlength;
    }

    public void setCarlength(Integer carlength) {
        this.carlength = carlength;
    }

    public String getVd_type() {
        return vd_type;
    }

    public void setVd_type(String vd_type) {
        this.vd_type = vd_type;
    }

    public String getWrite_date() {
        return write_date;
    }

    public void setWrite_date(String write_date) {
        this.write_date = write_date;
    }

    public Integer getDate_stamp() {
        return date_stamp;
    }

    public void setDate_stamp(Integer date_stamp) {
        this.date_stamp = date_stamp;
    }
}
