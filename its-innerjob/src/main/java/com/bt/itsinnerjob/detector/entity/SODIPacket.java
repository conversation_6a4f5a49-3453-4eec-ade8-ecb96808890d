package com.bt.itsinnerjob.detector.entity;

public class SODIPacket {
    public static final String HEAD = "011016F00130";

    public static final String END = "04";

    public static final int LENGTH = 67;

    private String frame;

    private String data;

    private String ip;

    public boolean FrameSplit(String frame) {
        if (frame != null && frame.length() >= 67) {
            this.frame = frame;
            this.data = frame.substring(20, frame.length() - 8);
            return true;
        }
        return false;
    }

    public static String getWeatherDataCommand() {
        String cmd = "0110013016f00f022f10066400c80059027102bc023403037b4e04".toUpperCase();
        return cmd;
    }

    public String getFrame() {
        return this.frame;
    }

    public void setFrame(String frame) {
        this.frame = frame;
    }

    public String getData() {
        return this.data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getIp() {
        return this.ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
}
