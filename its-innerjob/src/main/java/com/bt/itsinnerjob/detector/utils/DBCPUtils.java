package com.bt.itsinnerjob.detector.utils;

import org.apache.commons.dbcp2.BasicDataSourceFactory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.sql.DataSource;
import java.sql.*;
import java.util.Properties;

public class DBCPUtils {

    static Logger logger = LogManager.getLogger(LogManager.ROOT_LOGGER_NAME);

    private static DataSource dataSource;//定义一个连接池对象

    static {
        try {
            Properties prop = new Properties();
            prop.load(DBCPUtils.class.getClassLoader().getResourceAsStream("global.properties"));
            dataSource = BasicDataSourceFactory.createDataSource(prop);//得到一个连接池对象
        } catch (Exception e) {
            logger.error("初始化连接错误，请检查配置文件！");
        }
    }

    //从池中获取一个连接
    public static Connection getConnection() throws SQLException {
        return dataSource.getConnection();
    }

    public static void close3All(ResultSet rs, Statement stmt, Connection conn) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                logger.warn("数据库关闭错误:"+e.getMessage());
            }
        }

        if (stmt != null) {
            try {
                stmt.close();
            } catch (SQLException e) {
                logger.warn("数据库关闭错误:"+e.getMessage());
            }
        }

        if (conn != null) {
            try {
                conn.close();//关闭
            } catch (SQLException e) {
                logger.warn("数据库关闭错误:"+e.getMessage());
            }
        }
    }

    public static void close2All(PreparedStatement pstm, Connection conn) {
        if (pstm != null) {
            try {
                pstm.close();
            } catch (SQLException e) {
                logger.warn("数据库关闭错误:"+e.getMessage());
            }
        }

        if (conn != null) {
            try {
                conn.close();//关闭
            } catch (SQLException e) {
                logger.warn("数据库关闭错误:"+e.getMessage());
            }
        }
    }
}
