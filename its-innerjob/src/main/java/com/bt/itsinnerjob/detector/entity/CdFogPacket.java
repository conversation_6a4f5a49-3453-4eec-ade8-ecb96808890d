package com.bt.itsinnerjob.detector.entity;

import com.bt.itscore.domain.dto.FogInductStatusDTO;
import com.bt.itsinnerjob.detector.utils.DetectorUtils;

import java.util.Calendar;
import java.util.Locale;

/**
 * 畅电雾区帧结构
 */

public class CdFogPacket {
    public static final String FRAME_START = "A1";//帧头
    public static final String FRAME_TAILLENGTH = "52";//尾迹长度
    public static final String FRAME_CARSENSITIVITY = "32";//车检灵敏度
    public static final String FRAME_END = "F600";//帧尾
    public static String framid = "01";//初始指令序号，0x01~0xFF，要求每次发送“不同”内容的指令时，序号每次增加1

    private String cmd;
    private String data;
    private String frame;//完整的帧
    private String ip;//来源ip

    /*
    public static void main(String[] args) {
        System.out.println("查询雾灯主机工作状态："+ CdFogPacket.getTotalStatusCmd());
    //     System.out.println("开控制："+ CdFogPacket.openController());
    //    System.out.println("关控制："+ CdFogPacket.closeController());
     //   System.out.println("校时："+ CdFogPacket.timeSync());
        System.out.println("修改工作模式0："+ CdFogPacket.getModifyWorkModeCmd(2,0,30,1400));
        System.out.println("修改工作模式1："+ CdFogPacket.getModifyWorkModeCmd(3,1,30,1400));
        System.out.println("修改工作模式2："+ CdFogPacket.getModifyWorkModeCmd(4,2,30,1400));
        System.out.println("修改工作模式3："+ CdFogPacket.getModifyWorkModeCmd(5,7,30,1400));
    }
     */

    //帧结构划分，提取各部分内容
    public boolean FrameSplit(String frame) {
        if (frame != null && frame.length() >= 18) {
            //去除帧头帧尾
            String cmd = frame.substring(12, 14);
            //crc校验只有1位了
            String data = frame.substring(14, frame.length() - 2);
            this.cmd = cmd;
            this.data = data;
            return true;
        } else
            return false;
    }

    //查询雾灯主机的工作状态
    public static String getTotalStatusCmd()
    {

        String hex = FRAME_START+"01"+"88"+"0000000000"+FRAME_TAILLENGTH+FRAME_CARSENSITIVITY+FRAME_END;
        return hex;
    }

    public static FogInductStatusDTO getStatusEntityFromResponse(String packet, FogInductStatusDTO status)
    {
        if(packet.length()>=24) {
            String data = packet.substring(4, 18);
            //1字节工作模式(0,1,2,7)//匹配前端对应模式
            Integer workMode = Integer.parseInt(data.substring(0,2),16);
            //1字节闪烁频率
            Integer frequency = Integer.parseInt(data.substring(2,4),16);
            //2字节黄灯亮度
            Integer lightLevely = Integer.parseInt(data.substring(4,8),16);
            //2字节红灯亮度
            Integer lightLevelr = Integer.parseInt(data.substring(8,12),16);
            //1字节尾迹长度
          //  Integer taiLength = Integer.parseInt(data.substring(12,14),16);

            lightLevely=lightPWMToCd(lightLevely); //PWM值转为cd值，黄灯
            lightLevelr=lightPWMToCd(lightLevelr); //PWM值转为cd值，红灯
            status.setFlashFrequency(frequency);
            //根据工作模式获取对应的灯的亮度
            if(workMode ==1 ||workMode ==2 ) //黄灯
            {
                status.setLightLevel(lightLevely);
            }
           else if(workMode ==7 ) //红灯
            {
                status.setLightLevel(lightLevelr);
            }
            workMode=workModeMatch(workMode);  //畅电雾灯工作模式转为云控预案工作模式
            status.setWorkMode(workMode);
        }
        return status;
    }


    public static String openController()
    {
        String hex = "EEA500CCFFFF020301";
        String crc = DetectorUtils.crc8MAXIM(hex);
        return hex+crc;
    }

    public static String closeController()
    {
        String hex = "EEA500CCFFFF020300";
        String crc = DetectorUtils.crc8MAXIM(hex);
        return hex+crc;
    }

    public static boolean generalResponse(String packet)
    {
        if(!packet.contains("EEA5"))
        {
            return false;
        }
        packet = packet.substring(packet.indexOf("EEA5"));
        if(packet.length()<18)
            return false;
        //从EEA5开始找
        //取响应答复
        String resp = packet.substring(16,18);
        return (resp!=null&&resp.equals("00"));
    }


    public static String timeSync()
    {
        String hex = "EEA500CCFFFF0705";
        Calendar c = Calendar.getInstance();
        String time = String.format("%02x",Integer.parseInt((c.get(Calendar.YEAR)+"").substring(2)))+String.format("%02x",c.get(Calendar.MONTH)+1)+
                String.format("%02x",c.get(Calendar.DAY_OF_MONTH))+String.format("%02x",c.get(Calendar.HOUR_OF_DAY))+
                String.format("%02x",c.get(Calendar.MINUTE))+String.format("%02x",c.get(Calendar.SECOND));
        String crc = DetectorUtils.crc8MAXIM((hex+time).toUpperCase(Locale.ROOT));
        return (hex+time+crc).toUpperCase(Locale.ROOT);
    }

    /**
     * 修改工作模式
     * @param type 模式类型
     * 0x00: 雾灯分机关灯，字节4~字节10都可以填00;
     * 0x01: 雾灯分机黄灯常亮，这时指令字节4=3C; 字节5+字节6=黄灯亮度；字节7~字节8都可以填00;
     * 0x02: 雾灯分机黄灯闪烁，这时指令字节4=1E/3C/78【30/60/120次/分钟，只能这三种】; 字节5+字节6=黄灯亮度；字节7~字节8都可以填00;
     * 0x07: 雾灯分机红灯模式，这时指令字节4=1E/3C/78【30/60/120次/分钟】; 字节5+字节6=黄灯亮度；字节7+字节8=红灯亮度；
     *
     * @param frequency 频率（次/min）（30/60/120）可选值 HEX（1E/3C/78）
     * @param lightLevel 黄灯亮 度 ( cd )（ 500/1000/1500/2500/3500/4500/5700/7000 ） 可 选 值 HEX（0578/0B54/125C/2008/2EE0/3A98/4E20/7530）
     * @return
     */
    public static String getModifyWorkModeCmd(Integer cmdId,Integer type,Integer frequency,Integer lightLevel)
    {
        //*******构造不同工作模式的指令
        String cmd="";   //帧头+指令序号+工作模式+闪烁频率+黄灯亮度+红灯亮度+尾迹长度+车检灵敏度+帧尾;
        switch (type) //type为十进制
        {
            case 0: //雾灯分机关灯
                cmd = FRAME_START+String.format("%02x",cmdId)+String.format("%02x",type)+"00000000000000"+FRAME_END;
                break;
            case 1: //雾机分灯-黄灯常亮
                cmd = FRAME_START+String.format("%02x",cmdId)+String.format("%02x",type)+"3C"+String.format("%04x",lightLevel)+"0000"+FRAME_TAILLENGTH+FRAME_CARSENSITIVITY+FRAME_END;;
                break;
            case 2: //雾机分灯-黄灯闪烁
                cmd = FRAME_START+String.format("%02x",cmdId)+String.format("%02x",type)+String.format("%02x",frequency)
                        +String.format("%04x",lightLevel)+"0000"+FRAME_TAILLENGTH+FRAME_CARSENSITIVITY+FRAME_END;
                break;
            case 7: //雾机分灯-红灯模式
                cmd=FRAME_START+String.format("%02x",cmdId)+String.format("%02x",type)+String.format("%02x",frequency)
                        +"0000"+String.format("%04x",lightLevel)+FRAME_TAILLENGTH+FRAME_CARSENSITIVITY+FRAME_END;
                break;
        }

        return cmd;
    }

    //修改工作模式响应校验
    public static boolean getModifyWorkModeCmdResponse(String packet)
    {
        //畅电雾区修改工作模式响应检验，根据帧头A1和帧尾F6
        String parameters=packet.substring(4,18);  //发送的参数
        if(!packet.startsWith("A1")||!packet.substring(21,23).equals("F6"))
        {
            return false;
        }

        //    if()
        return true;
        /*
        packet = packet.substring(packet.indexOf("EEA5"));
        if(packet.length()<18)
            return false;
        //取响应答复
        String resp = packet.substring(16,18);
        return (resp!=null&&resp.equals("00"));
        */
    }

    /**
     * 防追尾距离及超时时间设置
     * @param mode 防追尾红灯熄灭模式 0x01 表示依据 PARA2 指定长度的雾灯个数熄灭，PARA3 作为最大超时时间，当达到超 时时间后会自动进入黄闪；（出厂默认模式，6 个，可设置 03-0A） 0x00 表示只依据超 PARA3 超时时间，PARA2 无效
     * @param distance 警示距离米 黄灯按预设频率闪烁，有车通过诱导灯时触发上游指定 PARA2 个雾灯红色点亮，距离 20* （PARA2-1）米，默认 6 个，100 米
     * @param sec 超时时间，触发红闪最大保持时间 PARA1 秒（默认 0x03 3 秒）
     * @return
     */
    public static String getModifyAlarmDistanceAndTime(int mode,int distance,int sec)
    {
        if(distance<40||distance>180)
        {
            distance = 100;
        }
        int lightNumber = distance/20+1;
        String cmd = "EEA500CCFFFF0423"+String.format("%02x",mode)+String.format("%02x",lightNumber)+String.format("%02x",sec);
        String crc = DetectorUtils.crc8MAXIM(cmd);
        return cmd+crc;
    }

    public static boolean getModifyAlarmDistanceAndTimeResponse(String packet)
    {
        if(!packet.contains("EEA5"))
        {
            return false;
        }
        packet = packet.substring(packet.indexOf("EEA5"));
        if(packet.length()<18)
            return false;
        //取响应答复
        String resp = packet.substring(16,18);
        return (resp!=null&&resp.equals("00"));
    }


    public static String getVisibilityCmd()
    {
        return "EEA500CCFFFF32FFEE";
    }

    public static Double getVisibilityFromResponse(String packet)
    {
        String str = DetectorUtils.hexStringToString(packet);
        if(str.startsWith("Visibility:"))
        {
            str = str.replace("\r","");
            str = str.replace("\n","");
            if(str.endsWith("M"))
            {
                str = str.substring(0,str.length()-1);
            }
            return Double.parseDouble(str.substring(str.indexOf(":")+1));
        }
        else
        {
            return 0.0;
        }
    }

    //十进制数格式化为需要的十六进制string
    private static String formatBitHex(int... number) {
        StringBuffer sb = new StringBuffer();
        for (int n : number) {
            String temp = Integer.toHexString(n);
            if (temp.length() < 2)
                temp = "0" + temp;
            sb.append(temp);
        }
        return sb.toString();
    }

    public static int lightPWMToCd(int lightPWM)
    {
        //PWM转为实际亮度值
        int lightcd=1500;
        switch (lightPWM){
            case 1400:
                lightcd=500;
                break;
            case 2900:
                lightcd=1000;
                break;
            case 4750:
                lightcd=1500;
                break;
            case 8200:
                lightcd=2500;
                break;
            case 11500:
                lightcd=3500;
                break;
            case 15200:
                lightcd=4500;
                break;
            case 21000:
                lightcd=5700;
                break;
            case 28000:
                lightcd=7000;
                break;
        }
        return lightcd;
    }

    //畅电工作模式转为预案工作模式
    public static int  workModeMatch(int WorkMode) {
        int result=8; //默认设置为黄闪
        switch (WorkMode){
            case 2: //黄闪
                result=8;
                break;
            case 0: //雾灯关闭
                result=5;
                break;
            case 1: //黄灯常亮
                result=6;
                break;
            case 7: //红灯闪烁
                result=9;
                break;
        }
        return result;
    }




    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public String getFrame() {
        return frame;
    }

    public String getData() {
        return data;
    }

    public String getIp() {
        return ip;
    }

    public void setFrame(String frame) {
        this.frame = frame;
    }

    public void setData(String data) {
        this.data = data;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
}
