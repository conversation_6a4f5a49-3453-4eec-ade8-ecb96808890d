package com.bt.itsinnerjob.detector.entity;


import com.bt.itsinnerjob.detector.utils.*;
import org.apache.commons.lang.StringUtils;

/**
 * 交调站协议帧结构
 */

public class TrafficPacket {
    public static final String FRAME_START = "AAAA";//帧头
    public static final String FRAME_END = "EEEE";//帧尾

    public static final String ERROR_CRC_WRONG = "1201";//CRC错误要求重传指令码
    public static final String ERROR_FORMAT_WRONG = "1301";//格式错误要求重传指令码

    private int length;//帧数据长度
    private String data;//帧数据
    private String crc;//校验位
    private String frame;//完整的帧
    private String ip;//源IP
    private int dataType;//数据包类型


//    public static void main(String[] args) {
//        System.out.println(TrafficPacket.generateCRC(
//                "410009303037313234303332303132303239376C6E6A79787867733838383838383838E607010B97009700"
//        ));
//    }


    public TrafficPacket() {
    }

    public TrafficPacket(String data) {
        if (data != null && data.length() > 0) {
            if (data.length() % 2 == 1)
                this.data = data + "0";
            this.length = data.length() / 2;
            String lenAndData = to2ByteHex(this.length) + data;
            //CRC校验
            this.crc = generateCRC(lenAndData);
            //组合成完整的帧
            this.frame = FRAME_START + lenAndData + crc + FRAME_END;
        }
    }

    //将完整的帧分割并提取相应内容
    public static boolean FrameSplit(TrafficPacket packet) {
        if (packet.frame != null && packet.frame.length() > 16) {
            //去除帧头帧尾
            String dataWithoutStartAndEnd = packet.frame.substring(4, packet.frame.length() - 4);
            //获取长度和校验位
            String lenHexStr = dataWithoutStartAndEnd.substring(0, 4);
            lenHexStr = lenHexStr.substring(2, 4) + lenHexStr.substring(0, 2);
            int len = Integer.parseInt(lenHexStr, 16);
            String crc = dataWithoutStartAndEnd.substring(dataWithoutStartAndEnd.length() - 4);
            packet.length = len;
            packet.crc = crc;
            //获取数据
            String data = dataWithoutStartAndEnd.substring(4, dataWithoutStartAndEnd.length() - 4);
            packet.data = data;
            return true;
        } else
            return false;
    }

    public static TrafficPacket FrameSplit(String frame) {
        TrafficPacket packet = new TrafficPacket();
        if (frame != null && frame.length() > 16) {
            //去除帧头帧尾
            String dataWithoutStartAndEnd = frame.substring(4, frame.length() - 4);
            //获取长度和校验位
            String lenHexStr = dataWithoutStartAndEnd.substring(0, 4);
            lenHexStr = lenHexStr.substring(2, 4) + lenHexStr.substring(0, 2);
            int len = Integer.parseInt(lenHexStr, 16);
            String crc = dataWithoutStartAndEnd.substring(dataWithoutStartAndEnd.length() - 4);
            packet.length = len;
            packet.crc = crc;
            //获取数据
            String data = dataWithoutStartAndEnd.substring(4, dataWithoutStartAndEnd.length() - 4);
            packet.data = data;
            return packet;
        } else
            return null;
    }

    //帧格式错误要求设备重传指令
    public String getDataFormatWrongAnswer() {
        TrafficData trafficData = new TrafficData();
        boolean flag = trafficData.getBasicInfo(this.getData());
        //发送格式错误的回复给交调站
        String answerCRCError = null;
        if (flag) {
            answerCRCError = this.getRealtimeDataAnswerToRD(2, trafficData.getTimeCode(), TrafficPacket.ERROR_FORMAT_WRONG);
        }
        return answerCRCError;
    }

    //帧CRC校验错误要求设备重传指令
    public TrafficPacket getCRCWrongAnswer() {
        TrafficData trafficData = new TrafficData();
        boolean flag = trafficData.getBasicInfo(this.getData());
        //发送CRC校验错误的回复给交调站
        String answerCRCError = null;
        if (flag) {
            answerCRCError = this.getRealtimeDataAnswerToRD(2, trafficData.getTimeCode(), TrafficPacket.ERROR_CRC_WRONG);
        }
        TrafficPacket packet = new TrafficPacket();
        packet.setFrame(answerCRCError);
        return packet;
    }

    //时间查询
    public TrafficPacket infoQuery() {
        String identification = getIdentification();
        if (identification != null) {
            String data = "02" + identification + String.format("%02x", 9);
            TrafficPacket packet = new TrafficPacket(data);
            return packet;
        }
        return null;
    }

    //对交调站实时数据包正确性的应答
    public String getRealtimeDataAnswerToRD(int dataPacketType, int timeCode, String errorCode) {
        String data = String.format("%02x", dataPacketType) + DetectorUtils.toRightOrderHex(timeCode, 2) + errorCode;
        TrafficPacket packet = new TrafficPacket(data);
        return packet.frame;
    }

    //应答设备通讯链路查询数据包
    public String answerLinkStatusToRD(int answerCode) {
        String identification = getIdentification();
        if (identification != null) {
            String data = "02" + identification + String.format("%02x", answerCode);
            TrafficPacket packet = new TrafficPacket(data);
            return packet.frame;
        } else {
            return null;
        }
    }

    //CRC校验
    public boolean checkCRC() {
        String lenAndData = to2ByteHex(this.length) + this.data;
        //CRC校验
        String crc = generateCRC(lenAndData);
        return (!StringUtils.isBlank(crc) && !StringUtils.isBlank(this.crc) && this.crc.equals(crc));
    }

    //获取数据类型
    public int getDataType() {
        if (data.length() < 2)
            return -1;
        this.dataType = Integer.parseInt(data.substring(0, 2), 16);
        return this.dataType;
    }

    //获取设备标识码
    public String getIdentification() {
        String identification = null;
        if (data.length() >= 34) {
            identification = data.substring(2, 34);
        }
        return identification;
    }

    //-----------------------------------------CRC生成------------------------------------------------------------------
    public static String generateCRC(String str) {
        char[] chars = str.toCharArray();
        //获取长度
        Integer legthnum = str.length() / 2;
        //动态建立数组
        byte[] Buffer = new byte[legthnum];
        for (int i = 0; i < Buffer.length; i++) {
            String hex = str.substring(i * 2, i * 2 + 2);
            int h = Integer.parseInt(hex, 16);
            Buffer[i] = (byte) h;
        }
        int num = CRC16(Buffer, Buffer.length); //结果为：57503 -》E09F
        String lastCRC = DetectorUtils.addZero(Integer.toHexString(num).toUpperCase(), 4);
        return lastCRC;
    }

    private static int CRC16(byte[] puchMsg, int usDataLen) {
        short uchCRCHI = 0xFF;
        short uchCRCLo = 0xFF;
        int uIndex;
        for (int i = 0; i < usDataLen; i++) {
            uIndex = (uchCRCHI ^ puchMsg[i]) & 0xff;
            uchCRCHI = (byte) (uchCRCLo ^ auchCRCHi[uIndex]);
            uchCRCLo = auchCRCLo[uIndex];
        }
        return ((((int) uchCRCHI) << 8 | (((int) uchCRCLo) & 0xff))) & 0xffff;
    }

    //高位字节表
    private static byte[] auchCRCHi = {
            0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x01, (byte) 0xC0, (byte) 0x80,
            0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1,
            (byte) 0x81, 0x40, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x01,
            (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x00, (byte) 0xC1, (byte) 0x81, 0x40,
            0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x01, (byte) 0xC0, (byte) 0x80,
            0x41, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x01, (byte) 0xC0,
            (byte) 0x80, 0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x01,
            (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x01, (byte) 0xC0, (byte) 0x80, 0x41,
            0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x00, (byte) 0xC1, (byte) 0x81,
            0x40, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1,
            (byte) 0x81, 0x40, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x00,
            (byte) 0xC1, (byte) 0x81, 0x40, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x01, (byte) 0xC0, (byte) 0x80, 0x41,
            0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x01, (byte) 0xC0, (byte) 0x80,
            0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x01, (byte) 0xC0,
            (byte) 0x80, 0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x01,
            (byte) 0xC0, (byte) 0x80, 0x41, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40,
            0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x00, (byte) 0xC1, (byte) 0x81,
            0x40, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x01, (byte) 0xC0,
            (byte) 0x80, 0x41, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x01,
            (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x00, (byte) 0xC1, (byte) 0x81, 0x40,
            0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1, (byte) 0x81,
            0x40, 0x00, (byte) 0xC1, (byte) 0x81, 0x40, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x00, (byte) 0xC1,
            (byte) 0x81, 0x40, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x01, (byte) 0xC0, (byte) 0x80, 0x41, 0x00,
            (byte) 0xC1, (byte) 0x81, 0x40
    };

    //低位字节表
    private static byte[] auchCRCLo = {

            0x00, (byte) 0xC0, (byte) 0xC1, 0x01, (byte) 0xC3, 0x03, 0x02, (byte) 0xC2, (byte) 0xC6, 0x06,
            0x07, (byte) 0xC7, 0x05, (byte) 0xC5, (byte) 0xC4, 0x04, (byte) 0xCC, 0x0C, 0x0D, (byte) 0xCD,
            0x0F, (byte) 0xCF, (byte) 0xCE, 0x0E, 0x0A, (byte) 0xCA, (byte) 0xCB, 0x0B, (byte) 0xC9, 0x09,
            0x08, (byte) 0xC8, (byte) 0xD8, 0x18, 0x19, (byte) 0xD9, 0x1B, (byte) 0xDB, (byte) 0xDA, 0x1A,
            0x1E, (byte) 0xDE, (byte) 0xDF, 0x1F, (byte) 0xDD, 0x1D, 0x1C, (byte) 0xDC, 0x14, (byte) 0xD4,
            (byte) 0xD5, 0x15, (byte) 0xD7, 0x17, 0x16, (byte) 0xD6, (byte) 0xD2, 0x12, 0x13, (byte) 0xD3,
            0x11, (byte) 0xD1, (byte) 0xD0, 0x10, (byte) 0xF0, 0x30, 0x31, (byte) 0xF1, 0x33, (byte) 0xF3,
            (byte) 0xF2, 0x32, 0x36, (byte) 0xF6, (byte) 0xF7, 0x37, (byte) 0xF5, 0x35, 0x34, (byte) 0xF4,
            0x3C, (byte) 0xFC, (byte) 0xFD, 0x3D, (byte) 0xFF, 0x3F, 0x3E, (byte) 0xFE, (byte) 0xFA, 0x3A,
            0x3B, (byte) 0xFB, 0x39, (byte) 0xF9, (byte) 0xF8, 0x38, 0x28, (byte) 0xE8, (byte) 0xE9, 0x29,
            (byte) 0xEB, 0x2B, 0x2A, (byte) 0xEA, (byte) 0xEE, 0x2E, 0x2F, (byte) 0xEF, 0x2D, (byte) 0xED,
            (byte) 0xEC, 0x2C, (byte) 0xE4, 0x24, 0x25, (byte) 0xE5, 0x27, (byte) 0xE7, (byte) 0xE6, 0x26,
            0x22, (byte) 0xE2, (byte) 0xE3, 0x23, (byte) 0xE1, 0x21, 0x20, (byte) 0xE0, (byte) 0xA0, 0x60,
            0x61, (byte) 0xA1, 0x63, (byte) 0xA3, (byte) 0xA2, 0x62, 0x66, (byte) 0xA6, (byte) 0xA7, 0x67,
            (byte) 0xA5, 0x65, 0x64, (byte) 0xA4, 0x6C, (byte) 0xAC, (byte) 0xAD, 0x6D, (byte) 0xAF, 0x6F,
            0x6E, (byte) 0xAE, (byte) 0xAA, 0x6A, 0x6B, (byte) 0xAB, 0x69, (byte) 0xA9, (byte) 0xA8, 0x68,
            0x78, (byte) 0xB8, (byte) 0xB9, 0x79, (byte) 0xBB, 0x7B, 0x7A, (byte) 0xBA, (byte) 0xBE, 0x7E,
            0x7F, (byte) 0xBF, 0x7D, (byte) 0xBD, (byte) 0xBC, 0x7C, (byte) 0xB4, 0x74, 0x75, (byte) 0xB5,
            0x77, (byte) 0xB7, (byte) 0xB6, 0x76, 0x72, (byte) 0xB2, (byte) 0xB3, 0x73, (byte) 0xB1, 0x71,
            0x70, (byte) 0xB0, 0x50, (byte) 0x90, (byte) 0x91, 0x51, (byte) 0x93, 0x53, 0x52, (byte) 0x92,
            (byte) 0x96, 0x56, 0x57, (byte) 0x97, 0x55, (byte) 0x95, (byte) 0x94, 0x54, (byte) 0x9C, 0x5C,
            0x5D, (byte) 0x9D, 0x5F, (byte) 0x9F, (byte) 0x9E, 0x5E, 0x5A, (byte) 0x9A, (byte) 0x9B, 0x5B,
            (byte) 0x99, 0x59, 0x58, (byte) 0x98, (byte) 0x88, 0x48, 0x49, (byte) 0x89, 0x4B, (byte) 0x8B,
            (byte) 0x8A, 0x4A, 0x4E, (byte) 0x8E, (byte) 0x8F, 0x4F, (byte) 0x8D, 0x4D, 0x4C, (byte) 0x8C,
            0x44, (byte) 0x84, (byte) 0x85, 0x45, (byte) 0x87, 0x47, 0x46, (byte) 0x86, (byte) 0x82, 0x42,
            0x43, (byte) 0x83, 0x41, (byte) 0x81, (byte) 0x80, 0x40
    };

    //-----------------------------------------------------------------------------------------------------------------

    public static String to2ByteHex(int len) {
        String hex = Integer.toHexString(len);
        hex = DetectorUtils.addZero(hex, 4);
        //小序输出结果
        hex = hex.substring(2, 4) + hex.substring(0, 2);
        return hex;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getCrc() {
        return crc;
    }

    public void setCrc(String crc) {
        this.crc = crc;
    }

    public String getFrame() {
        return frame;
    }

    public void setFrame(String frame) {
        this.frame = frame;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
}
