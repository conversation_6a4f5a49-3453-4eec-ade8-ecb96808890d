package com.bt.itsinnerjob.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.bt.itsinnerjob.service.LightprotAlarmService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

@JobHandler(value="lightprotAlarmHandler")
@Component
public class LightprotAlarmHandler extends IJobHandler {
	@Autowired
	LightprotAlarmService lightprotAlarmService;
	
	@Override
	public ReturnT<String> execute(String param) throws Exception {
		lightprotAlarmService.retry();
		XxlJobLogger.log("LightprotAlarmHandler end.");
        return SUCCESS;
	}


}
