package com.bt.itsinnerjob.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.bt.itsinnerjob.service.BayonetService;
import com.bt.itsinnerjob.utils.InnerjobUtils;
import com.google.gson.JsonSyntaxException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

/**
 * 获取高清卡口按小时的车流统计 推送到 阿里云
 * <AUTHOR>
 *
 */
@JobHandler(value="bayonetFlowHandler")
@Component
public class BayonetFlowHandler extends IJobHandler {
	@Autowired
	private BayonetService bayonetService;
	
	@Override
	public ReturnT<String> execute(String param) throws Exception {
	    XxlJobLogger.log("BayonetFlowHandler start.");
	    long startTime = System.currentTimeMillis();
		try {
			bayonetService.repushCarFlow();
		} catch (JsonSyntaxException e) {
			XxlJobLogger.log("BayonetFlowHandler JsonSyntaxException.{}", e.getMessage());
		}
		XxlJobLogger.log("任务耗时：{}", InnerjobUtils.calcConsumedTime(startTime));
        XxlJobLogger.log("BayonetFlowHandler end.");
        return SUCCESS;
	}
}
