package com.bt.itsinnerjob.utils;

public class InnerjobUtils {

    /**
     * @描述 startTime到现在多长时间（XX分XX秒）
     */
    public static String calcConsumedTime(long startTime) {
        long time = (System.currentTimeMillis() - startTime) / 1000;
        String consumedTime = time + "秒";
        if (time > 60) {
            long minutes = (time / 60);
            consumedTime = (minutes + "分") + (time % 60 == 0 ? "" : (time % 60 + "秒"));
        }
        return consumedTime;
    }

}
