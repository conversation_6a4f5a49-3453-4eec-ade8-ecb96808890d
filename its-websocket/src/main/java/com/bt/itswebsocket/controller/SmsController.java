package com.bt.itswebsocket.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bt.itscore.domain.dto.SmsTemplateDTO;
import com.bt.itswebsocket.server.SmsService;

@RestController
@RequestMapping("sms")
public class SmsController {
	@Autowired
	private SmsService smsService;

	@PostMapping("send")
	public boolean send(@RequestBody SmsTemplateDTO smsTemplateDTO) {
		boolean success = smsService.send(smsTemplateDTO);
		return success;
	}
}
