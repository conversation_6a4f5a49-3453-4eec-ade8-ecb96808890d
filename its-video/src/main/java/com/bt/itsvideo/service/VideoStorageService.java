package com.bt.itsvideo.service;

import com.bt.itscore.enums.SourceIdEnum;
import com.bt.itsvideo.mapper.VideoStorageMapper;

import org.apache.commons.lang.math.NumberUtils;
import org.apache.cxf.Bus;
import org.apache.cxf.BusFactory;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.apache.cxf.transport.http.HTTPConduitConfigurer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

@Service("VideoStorageService")
public class VideoStorageService {
    private final static Logger LOGGER = LoggerFactory.getLogger(VideoService.class);
    private static final String WSDL = "?wsdl";

    @Value("${video.auth.username}")
    String userName;
    @Value("${video.auth.password}")
    String password;

    @Value("${video.source.suffix}")
    private String suffix;
    @Value("${video.source.yanhai}")
    private String yanhaiHost;
    @Value("${video.source.luocheng}")
    private String luochengHost;
    @Value("${video.source.dahua}")
    private String dahuaHost;
    @Value("${video.source.zhaoping}")
    private String zhaopingHost;
    @Value("${video.source.lingshan}")
    private String lingshanHost;
    @Value("${video.source.laibin}")
    private String laibinHost;
    @Value("${video.source.nanning}")
    private String nanningHost;
    @Value("${video.source.qinzhou}")
    private String qinzhouHost;
    @Value("${video.source.beihai}")
    private String beihaiHost;

    @Autowired
    private VideoStorageMapper videoStorageMapper;

    private Map<String, Client> clientMap = new HashMap<>(); // 存储多个中心的连接

    /**
     * 根据摄像机id查询其归属的分中心地址
     * @param cameraCode 摄像机id
     * @return 分中心地址
     */
    public String source(String cameraCode) {
        return getDomainByCode(cameraCode) + suffix;
    }

    public String getDomainByCode(String cameraCode) {
        String sourceId = videoStorageMapper.source(cameraCode);
        String serviceUrl = "";
        LOGGER.info("sourceId:{}",sourceId);
        SourceIdEnum sourceIdEnum = SourceIdEnum.get(NumberUtils.toInt(sourceId));
        if(sourceIdEnum == null) {
        	LOGGER.warn("摄像头sourceId{}无对应节点", sourceId);
        } else {
        	switch (sourceIdEnum) {
                case YAN_HAI_1:
                    serviceUrl = yanhaiHost;
                    break;
                case LUO_CHENG_2:
                    serviceUrl = luochengHost;
                    break;
                case DA_HUA_3:
                    serviceUrl = dahuaHost;
                    break;
                case ZHAO_PING_4:
                    serviceUrl = zhaopingHost;
                    break;
                case LING_SHAN_5:
                    serviceUrl = lingshanHost;
                    break;
                case LAI_BIN_6:
                    serviceUrl = laibinHost;
                    break;
                case NAN_NING_7:
                    serviceUrl = nanningHost;
                    break;
                case QIN_ZHOU_8:
                    serviceUrl = qinzhouHost;
                    break;
                case BEI_HAI_9:
                    serviceUrl = beihaiHost;
                    break;
                default:
                    LOGGER.warn("摄像头sourceId{}无对应节点", sourceId);
                    break;
			}
        }

        return serviceUrl;
    }

    /**
     * 根据wsdl地址与摄像机id，创建webservice连接
     * @param url wsdl url
     * @param cameraCode 摄像机id，用于获取分中心的sourceid
     * @return 连接client
     */
    public Client createClientWithAuth(String url, String cameraCode) {
        String sourceId = videoStorageMapper.source(cameraCode);
        Client client = clientMap.get(sourceId);

        if(client == null) {
            final VideoService.MyHTTPConduitConfigurer myHttpConduitConfig = new VideoService.MyHTTPConduitConfigurer(userName, password);
            final Bus bus = BusFactory.getThreadDefaultBus();

            try {
                bus.setExtension(myHttpConduitConfig, HTTPConduitConfigurer.class);
                final JaxWsDynamicClientFactory dcf = JaxWsDynamicClientFactory.newInstance(bus);

                // 创建连接
                String fullUrl = url + WSDL;
                LOGGER.info("create connection to {}", fullUrl);
                client = dcf.createClient(fullUrl);

                clientMap.put(sourceId, client);
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
        return client;
    }
}
