package com.bt.itsvideo.controller;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bt.itscore.auth.Login;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.exception.ArgumentException;
import com.bt.itscore.utils.ValidUtils;
import com.bt.itsvideo.domain.dto.UavPlayDTO;
import com.bt.itsvideo.domain.dto.UavPtzDTO;
import com.bt.itsvideo.domain.dto.UavSbDTO;
import com.bt.itsvideo.service.UavService;

/**
 * 
 * @Description: 无人机系统对接服务
 * <AUTHOR>
 * @date 2025年3月10日 上午9:38:12
 *
 */
@RestController
@RequestMapping("uav")
public class UavController {
	@Autowired
	private UavService uavService;

	/**
	 * @描述 消息推送订阅
	 * @param dto
	 * @return
	 */
	@Login
	@PostMapping("subscribe")
	public Object subscribe(@RequestBody UavSbDTO dto) {
		if (dto.getDevices().isEmpty()) {
			throw new ArgumentException("订阅设备列表不能为空！");
		}
		if (StringUtils.isBlank(dto.getDevice_event())) {
			throw new ArgumentException("订阅事件类型不能为空！");
		}
		return uavService.subscribe(dto);
	}

	/**
	 * @描述 取消消息推送订阅
	 * @param dto
	 * @return
	 */
	@Login
	@PostMapping("unSubscribe")
	public Object unSubscribe(@RequestBody UavSbDTO dto) {
		if (dto.getDevices().isEmpty()) {
			throw new ArgumentException("取消订阅设备列表不能为空！");
		}
		if (StringUtils.isBlank(dto.getDevice_event())) {
			throw new ArgumentException("取消订阅事件类型不能为空！");
		}
		return uavService.unSubscribe(dto);
	}

	/**
	 * @描述 订阅所有设备消息
	 * @return
	 */
	@Login
	@PostMapping("allSubscribe")
	public Object allSubscribe() {
		return new ResponseVO(uavService.allSubscribe());
	}

	/**
	 * @描述 取消全部订阅信息
	 * @return
	 */
	@Login
	@PostMapping("cancelAllSubscribe")
	public Object cancelAllSubscribe() {
		return new ResponseVO(uavService.cancelAllSubscribe());
	}

	/**
	 * @描述 无人机视频播放-获取当前工作空间的直播能力
	 * 根据device_sn判断，有获取单个设备，无参工作空间
	 * @return
	 */
	@Login
	@PostMapping("capacity")
	public Object capacity(@RequestBody UavPlayDTO dto) {
		return uavService.capacity(dto);
	}

	/**
	 * @描述 无人机视频播放-start-开始直播
	 * @return
	 */
	@Login
	@PostMapping("start")
	public Object start(@RequestBody UavPlayDTO dto) {
		if (StringUtils.isBlank(dto.getUrl_type())) {
			throw new ArgumentException("直播协议类型不能为空！");
		}
		if (StringUtils.isBlank(dto.getVideo_id())) {
			throw new ArgumentException("直播视频流的ID不能为空！");
		}
		if (StringUtils.isBlank(dto.getVideo_quality())) {
			throw new ArgumentException("直播质量类型不能为空！");
		}
		return uavService.start(dto);
	}

	/**
	 * @描述 无人机视频播放-stop-停止直播
	 * @return
	 */
	@Login
	@PostMapping("stop")
	public Object stop(@RequestBody UavPlayDTO dto) {
		if (StringUtils.isBlank(dto.getUrl_type())) {
			throw new ArgumentException("停止直播协议类型不能为空！");
		}
		if (StringUtils.isBlank(dto.getVideo_id())) {
			throw new ArgumentException("停止直播视频流的ID不能为空！");
		}
		return uavService.stop(dto);
	}

	/**
	 * @描述 无人机视频播放-设置直播清晰度
	 * @return
	 */
	@Login
	@PostMapping("update")
	public Object update(@RequestBody UavPlayDTO dto) {
		if (StringUtils.isBlank(dto.getVideo_id())) {
			throw new ArgumentException("直播视频流的ID不能为空！");
		}
		if (StringUtils.isBlank(dto.getVideo_quality())) {
			throw new ArgumentException("直播质量类型不能为空！");
		}
		return uavService.update(dto);
	}

	/**
	 * @描述 无人机视频播放-切换直播镜头
	 * @return
	 */
	@Login
	@PostMapping("switch_lens")
	public Object switchLens(@RequestBody UavPlayDTO dto) {
		if (StringUtils.isBlank(dto.getVideo_id())) {
			throw new ArgumentException("直播视频流的ID不能为空！");
		}
		if (StringUtils.isBlank(dto.getVideo_type())) {
			throw new ArgumentException("直播协议类型不能为空！");
		}
		return uavService.switchLens(dto);
	}

	/**
	 * @描述 无人机视频播放-切换摄像机
	 * @return
	 */
	@Login
	@PostMapping("switch_camera")
	public Object switchCamera(@RequestBody UavPlayDTO dto) {
		if (StringUtils.isBlank(dto.getVideo_id())) {
			throw new ArgumentException("直播视频流的ID不能为空！");
		}
		if (StringUtils.isBlank(dto.getCamera_position())) {
			throw new ArgumentException("机场相机位置不能为空！");
		}
		return uavService.switchCamera(dto);
	}

	/**
	 * @描述 无人机-云台控制接口
	 * @return
	 */
	@Login
	@PostMapping("ptzControl")
	public Object ptzControl(@Valid @RequestBody UavPtzDTO dto, BindingResult result, HttpServletRequest request) {
		ValidUtils.error(result);
		return uavService.ptzControl(dto);
	}
	
	/**
	 * @描述 获取无人机状态
	 */
	@Login
	@PostMapping("getStatus")
	public Object getStatus(@Valid @RequestBody UavPlayDTO dto) {
		if (StringUtils.isBlank(dto.getDevice_sn())) {
			throw new ArgumentException("查询无人机编号不能为空！");
		}
		return uavService.getStatus(dto);
	}
	
	
}
