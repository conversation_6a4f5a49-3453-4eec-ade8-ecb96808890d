package com.bt.itsevent.constants;

import org.springframework.util.StringUtils;

import java.util.Locale;

/**
 * 描述：外呼机器人返回状态码枚举类
 *
 * <AUTHOR>
 * @since 2022-12-14
 * <p>
 * 0呼叫失败，1呼叫成功（已接通），2呼叫占线（被挂断），3呼叫无人接听
 */
public enum RobotCallEnum {
    FAILED(0, "failed", "呼叫失败"),
    SUCCEEDED(1, "succeeded", "呼叫成功-已接通"),
    BUSY(2, "busy", "未接通-占线"),
    NO_ANSWER(3, "noanswer", "未接通-无人接听"),
    NOT_EXIST(4, "notexist", "未呼出-号码不存在"), // 运营商从来都没推出过该号码
    CANCELLED(5, "cancelled", "未呼出-呼叫取消"),
    NOT_CONNECTED(6, "notconnected", "未接通-无法接通"),
    POWERED_OFF(7, "poweredoff", "未接通-关机"),
    OUT_OF_SERVICE(8, "outofservice", "未接通-停机"),
    IN_ARREARS(9, "inarrears", "未接通-欠费"),
    EMPTY_NUMBER(10, "emptynumber", "未接通-空号"), // 空号是指运营商存在过这个号码
    PER_DAY_CALL_COUNT_LIMIT(11, "perdaycallcountlimit", "未呼出-超出每日呼叫限制"),
    CONTACT_BLOCK_LIST(12, "contactblocklist", "未呼出-禁止外呼名单"),
    CALLER_NOT_REGISTERED(13, "callernotregistered", "未呼出-主叫号码未在中继注册"),
    TERMINATED(14, "terminated", "未呼出-被终止"),
    VERIFICATION_CANCELLED(15, "verificationcancelled", "未呼出-呼叫前校验不通过取消"),
    TIME_OUT(16, "", "呼叫失败-回执返回超时异常");

    private int code;
    private String codeDesc;
    private String result;

    RobotCallEnum(int code, String codeDesc, String result) {
        this.code = code;
        this.codeDesc = codeDesc;
        this.result = result;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getCodeDesc() {
        return codeDesc;
    }

    public void setCodeDesc(String codeDesc) {
        this.codeDesc = codeDesc;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public static RobotCallEnum getResultByCodeDesc(String codeDesc) {
        if (StringUtils.isEmpty(codeDesc)) {
            return null;
        }
        String descLowerCase = codeDesc.trim().toLowerCase(Locale.ROOT);
        RobotCallEnum[] values = RobotCallEnum.values();
        for (RobotCallEnum robotCallEnum : values) {
            if (descLowerCase.equals(robotCallEnum.getCodeDesc())) {
                return robotCallEnum;
            }
        }
        return null;
    }

}
