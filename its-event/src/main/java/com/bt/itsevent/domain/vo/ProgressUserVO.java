package com.bt.itsevent.domain.vo;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2022年11月21日 上午11:18:02
 * @Description 进展关联的用户和短信发送状态
 */
public class ProgressUserVO {
	private String eventId;// 事件id
	private Integer eventProgressId;// 进展id
	private String userId;// 用户id
	private String userName;// 用户名
	private String mobile;// 手机号
	private Integer cardPass;
	private Integer smsSendStatus;// 1成功，0失败
	private String smsFailReason;// 发送失败原因
	private String bizId;// 短信回执id
	private Timestamp sendTime;// 发送时间
	private String jobId;// 外呼任务id
	private String referenceId;// 绑定jobId以及返回的状态参数
	private Integer robotCallStatus;// 0呼叫失败，1呼叫成功（已接通），2呼叫占线（被挂断），3呼叫无人接听
	private String robotFailReason;// 外呼状态不是1的，全部判定要填写失败原因
	private Timestamp robotCallTime;// 外呼时间
	private Integer forceRemind;// 1-电话提醒，0-只短信提醒

	public String getEventId() {
		return eventId;
	}

	public void setEventId(String eventId) {
		this.eventId = eventId;
	}

	public Integer getEventProgressId() {
		return eventProgressId;
	}

	public void setEventProgressId(Integer eventProgressId) {
		this.eventProgressId = eventProgressId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public Integer getCardPass() {
		return cardPass;
	}

	public void setCardPass(Integer cardPass) {
		this.cardPass = cardPass;
	}

	public Integer getSmsSendStatus() {
		return smsSendStatus;
	}

	public void setSmsSendStatus(Integer smsSendStatus) {
		this.smsSendStatus = smsSendStatus;
	}

	public String getSmsFailReason() {
		return smsFailReason;
	}

	public void setSmsFailReason(String smsFailReason) {
		this.smsFailReason = smsFailReason;
	}

	public String getBizId() {
		return bizId;
	}

	public void setBizId(String bizId) {
		this.bizId = bizId;
	}

	public Timestamp getSendTime() {
		return sendTime;
	}

	public void setSendTime(Timestamp sendTime) {
		this.sendTime = sendTime;
	}

	public String getJobId() {
		return jobId;
	}

	public void setJobId(String jobId) {
		this.jobId = jobId;
	}

	public String getReferenceId() {
		return referenceId;
	}

	public void setReferenceId(String referenceId) {
		this.referenceId = referenceId;
	}

	public Integer getRobotCallStatus() {
		return robotCallStatus;
	}

	public void setRobotCallStatus(Integer robotCallStatus) {
		this.robotCallStatus = robotCallStatus;
	}

	public String getRobotFailReason() {
		return robotFailReason;
	}

	public void setRobotFailReason(String robotFailReason) {
		this.robotFailReason = robotFailReason;
	}

	public Timestamp getRobotCallTime() {
		return robotCallTime;
	}

	public void setRobotCallTime(Timestamp robotCallTime) {
		this.robotCallTime = robotCallTime;
	}

	public Integer getForceRemind() {
		return forceRemind;
	}

	public void setForceRemind(Integer forceRemind) {
		this.forceRemind = forceRemind;
	}
}
