package com.bt.itsevent.domain.vo;


import java.util.Map;

public class EventTypeTop5MapVO {
    private Integer sourceId;
    private Map<String,Integer> count;

    public  EventTypeTop5MapVO (){

    }

    public  EventTypeTop5MapVO (Integer sourceId,Map<String,Integer> count){
        this.sourceId = sourceId;
        this.count = count;
    }


    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public Map<String, Integer> getCount() {
        return count;
    }

    public void setCount(Map<String, Integer> count) {
        this.count = count;
    }
}
