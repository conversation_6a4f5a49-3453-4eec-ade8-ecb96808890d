package com.bt.itsevent.domain.vo;

import java.io.Serializable;

/**
 * 突发事件统计表
 *
 * @Author: QinShiheng
 * @Date: 2022年12月30日14:51
 * @Description:
 **/
public class EventStaticsVO implements Serializable {
    private String id; // 事件id
    private String reportTime; // 接报时间
    private String roadName; // 路段名称
    private String direction; // 方向
    private String mile; // 桩号（公里数）
    private String post; // 桩号（米）
    private Integer accidentNum; // 事故起数
    private Integer carNum; // 	事故车数
    private String carPlate; // 事故车牌
    private String lane; // 堵道情况
    private Integer secondAccidentNum; // 次生事故次数
    private String carCategory; // 事故车类
    private String pCarType; // 客车类型
    private String gCarType; //  货车类型
    private Integer gGoodsCarNum; // 载重货车数量
    private Integer gEmptyCarNum; // 空车货车数量
    private String goods; // 载重货车所载货物
    private String injureMan; // 受伤人数
    private String deathMan; // 	死亡人数
    private String roadType; // 路况类型
    private String facilityName; // 具体位置
    private String roadCondition; // 道路情况
    private String weather; // 天气
    private String accidentCause; // 造成事故原因
    private String progressBrief; // 事故简述
    private String suggestion; // 改进管理的意见和建议

    private String eventAnalyse;

    private String eventType;//事件类型
    private String finishUserName; // 填表人

    public String getEventAnalyse() {
        return eventAnalyse;
    }

    public void setEventAnalyse(String eventAnalyse) {
        this.eventAnalyse = eventAnalyse;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getReportTime() {
        return reportTime;
    }

    public void setReportTime(String reportTime) {
        this.reportTime = reportTime;
    }

    public String getRoadName() {
        return roadName;
    }

    public void setRoadName(String roadName) {
        this.roadName = roadName;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getMile() {
        return mile;
    }

    public void setMile(String mile) {
        this.mile = mile;
    }

    public String getPost() {
        return post;
    }

    public void setPost(String post) {
        this.post = post;
    }

    public Integer getAccidentNum() {
        return accidentNum;
    }

    public void setAccidentNum(Integer accidentNum) {
        this.accidentNum = accidentNum;
    }

    public Integer getCarNum() {
        return carNum;
    }

    public void setCarNum(Integer carNum) {
        this.carNum = carNum;
    }

    public String getCarPlate() {
        return carPlate;
    }

    public void setCarPlate(String carPlate) {
        this.carPlate = carPlate;
    }

    public String getLane() {
        return lane;
    }

    public void setLane(String lane) {
        this.lane = lane;
    }

    public Integer getSecondAccidentNum() {
        return secondAccidentNum;
    }

    public void setSecondAccidentNum(Integer secondAccidentNum) {
        this.secondAccidentNum = secondAccidentNum;
    }

    public String getCarCategory() {
        return carCategory;
    }

    public void setCarCategory(String carCategory) {
        this.carCategory = carCategory;
    }

    public String getpCarType() {
        return pCarType;
    }

    public void setpCarType(String pCarType) {
        this.pCarType = pCarType;
    }

    public String getgCarType() {
        return gCarType;
    }

    public void setgCarType(String gCarType) {
        this.gCarType = gCarType;
    }

    public Integer getgGoodsCarNum() {
        return gGoodsCarNum;
    }

    public void setgGoodsCarNum(Integer gGoodsCarNum) {
        this.gGoodsCarNum = gGoodsCarNum;
    }

    public Integer getgEmptyCarNum() {
        return gEmptyCarNum;
    }

    public void setgEmptyCarNum(Integer gEmptyCarNum) {
        this.gEmptyCarNum = gEmptyCarNum;
    }

    public String getGoods() {
        return goods;
    }

    public void setGoods(String goods) {
        this.goods = goods;
    }

    public String getInjureMan() {
        return injureMan;
    }

    public void setInjureMan(String injureMan) {
        this.injureMan = injureMan;
    }

    public String getDeathMan() {
        return deathMan;
    }

    public void setDeathMan(String deathMan) {
        this.deathMan = deathMan;
    }

    public String getRoadType() {
        return roadType;
    }

    public void setRoadType(String roadType) {
        this.roadType = roadType;
    }

    public String getFacilityName() {
        return facilityName;
    }

    public void setFacilityName(String facilityName) {
        this.facilityName = facilityName;
    }

    public String getRoadCondition() {
        return roadCondition;
    }

    public void setRoadCondition(String roadCondition) {
        this.roadCondition = roadCondition;
    }

    public String getWeather() {
        return weather;
    }

    public void setWeather(String weather) {
        this.weather = weather;
    }

    public String getAccidentCause() {
        return accidentCause;
    }

    public void setAccidentCause(String accidentCause) {
        this.accidentCause = accidentCause;
    }

    public String getProgressBrief() {
        return progressBrief;
    }

    public void setProgressBrief(String progressBrief) {
        this.progressBrief = progressBrief;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public String getFinishUserName() {
        return finishUserName;
    }

    public void setFinishUserName(String finishUserName) {
        this.finishUserName = finishUserName;
    }

	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}
}
