package com.bt.itsevent.domain.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 
 * @Description: 应急救援事件类型
 * @author: <PERSON>ng<PERSON>iQ<PERSON>g
 * @date: 2022年5月10日 下午4:56:55
 */
public class EventTypeRescueDTO {
    private String id;// id
    @NotBlank(message = "所属分类不能为空")
    private String pid;// 父级id
    @NotBlank(message = "救援事件类型名称不能为空")
    private String name;// 名称
    @NotNull(message = "是否启用不能为空")
    private Integer status;// 1：启用，0：未启用
    @NotEmpty(message = "适用范围不能为空")
    private List<String> orgIds;// 匹配运营公司
    private Integer level;// 事件类型级别 1，2，3，4，5级类型
    private String description;// 事件类型介绍
    private List<String> lables;// 判断标签
    private Integer value;//
    private Integer often;
    private String flag;// DD
    private String keyword;// 关键字搜索： 标签、名称

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<String> orgIds) {
        this.orgIds = orgIds;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public List<String> getLables() {
        return lables;
    }

    public void setLables(List<String> lables) {
        this.lables = lables;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public Integer getOften() {
        return often;
    }

    public void setOften(Integer often) {
        this.often = often;
    }

}
