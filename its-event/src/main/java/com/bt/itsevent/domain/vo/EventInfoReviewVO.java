package com.bt.itsevent.domain.vo;

import java.util.ArrayList;
import java.util.List;

import com.bt.itsevent.domain.dto.ProgressUserDTO;

public class EventInfoReviewVO {
	private String id;
	private String eventId;
	private String eventNo;
	private String progressDesc;
	private String createUserId;
	private String createUserName;
	private String reviewUserId;
	private String reviewUserName;
	private List<ProgressUserDTO> infoReviewUsers = new ArrayList<>();
	private Integer infoType; // 1初报 5续报 10 终报
	private Integer reviewStatus; // 0待审核，1通过，2不通过，3已撤回
	private String createTime; // 报送时间
	private String reviewTime; // 审核时间
	private String reviewRemark; // 审核意见
	private String eventDesc;
	private String reportTime;
	private String reportSource;
	private String location;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getEventId() {
		return eventId;
	}

	public void setEventId(String eventId) {
		this.eventId = eventId;
	}

	public String getEventNo() {
		return eventNo;
	}

	public void setEventNo(String eventNo) {
		this.eventNo = eventNo;
	}

	public String getProgressDesc() {
		return progressDesc;
	}

	public void setProgressDesc(String progressDesc) {
		this.progressDesc = progressDesc;
	}

	public String getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}

	public String getReviewUserId() {
		return reviewUserId;
	}

	public void setReviewUserId(String reviewUserId) {
		this.reviewUserId = reviewUserId;
	}

	public String getReviewUserName() {
		return reviewUserName;
	}

	public void setReviewUserName(String reviewUserName) {
		this.reviewUserName = reviewUserName;
	}

	public List<ProgressUserDTO> getInfoReviewUsers() {
		return infoReviewUsers;
	}

	public void setInfoReviewUsers(List<ProgressUserDTO> infoReviewUsers) {
		this.infoReviewUsers = infoReviewUsers;
	}

	public Integer getInfoType() {
		return infoType;
	}

	public void setInfoType(Integer infoType) {
		this.infoType = infoType;
	}

	public Integer getReviewStatus() {
		return reviewStatus;
	}

	public void setReviewStatus(Integer reviewStatus) {
		this.reviewStatus = reviewStatus;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getReviewTime() {
		return reviewTime;
	}

	public void setReviewTime(String reviewTime) {
		this.reviewTime = reviewTime;
	}

	public String getReviewRemark() {
		return reviewRemark;
	}

	public void setReviewRemark(String reviewRemark) {
		this.reviewRemark = reviewRemark;
	}

	public String getEventDesc() {
		return eventDesc;
	}

	public void setEventDesc(String eventDesc) {
		this.eventDesc = eventDesc;
	}

	public String getReportTime() {
		return reportTime;
	}

	public void setReportTime(String reportTime) {
		this.reportTime = reportTime;
	}

	public String getReportSource() {
		return reportSource;
	}

	public void setReportSource(String reportSource) {
		this.reportSource = reportSource;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}
}
