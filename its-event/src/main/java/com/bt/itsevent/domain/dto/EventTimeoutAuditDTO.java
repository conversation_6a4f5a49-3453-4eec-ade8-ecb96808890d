package com.bt.itsevent.domain.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年8月5日 下午1:07:12
 * @Description 事件处置超时申述DTO
 */
public class EventTimeoutAuditDTO {
    private Integer id;// 事件处置超时申述id
    @NotBlank(message = "事件id不能为空")
    private String eventId;// 事件id
    @NotBlank(message = "超时申述原因说明不能为空")
    @Size(max = 500, message = "超时申述原因说明不能超过500字符")
    private String reason;// 申述理由，超时申述原因说明
    private String createUserId;// 申述人id
    private String createUserName;// 申述人
    private Long createTime;// 记录生成时间
    private Long updateTime;// 记录修改时间
    private Integer auditStatus;// 审核状态，0待审核，1审核通过，2审核不通过
    private String eventNo;// 工单编号
    private String auditor;// 审核人
    private String auditorId;// 审核人id
    private Long startTime;// 开始时间（查询条件）
    private Long endTime;// 结束时间（查询条件）
    private int kefu = 0; // 是否是客服中心部门
    private List<String> roleIds;
    private String userId;
    private String auditOpinion;// 审批意见，最多限制100字符
    private List<Integer> attachs;// 事件超时申诉附件

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getEventNo() {
        return eventNo;
    }

    public void setEventNo(String eventNo) {
        this.eventNo = eventNo;
    }

    public String getAuditor() {
        return auditor;
    }

    public void setAuditor(String auditor) {
        this.auditor = auditor;
    }

    public String getAuditorId() {
        return auditorId;
    }

    public void setAuditorId(String auditorId) {
        this.auditorId = auditorId;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public int getKefu() {
        return kefu;
    }

    public void setKefu(int kefu) {
        this.kefu = kefu;
    }

    public List<String> getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(List<String> roleIds) {
        this.roleIds = roleIds;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAuditOpinion() {
        return auditOpinion;
    }

    public void setAuditOpinion(String auditOpinion) {
        this.auditOpinion = auditOpinion;
    }

    public List<Integer> getAttachs() {
        return attachs;
    }

    public void setAttachs(List<Integer> attachs) {
        this.attachs = attachs;
    }

}
