package com.bt.itsevent.controller;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bt.itscore.auth.CheckAuthz;
import com.bt.itscore.auth.Login;
import com.bt.itscore.domain.dto.AttachDTO;
import com.bt.itscore.domain.dto.IdStringBatchDTO;
import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.vo.PageVO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.utils.OssUtils;
import com.bt.itscore.utils.ValidUtils;
import com.bt.itsevent.domain.dto.EventVisitDTO;
import com.bt.itsevent.domain.dto.EventVisitForGzhDTO;
import com.bt.itsevent.domain.dto.EventWorkOrderAttachDTO;
import com.bt.itsevent.domain.dto.EventWorkOrderDTO;
import com.bt.itsevent.domain.dto.EventWorkOrderProgressDTO;
import com.bt.itsevent.domain.vo.EventWorkOrderProgressVO;
import com.bt.itsevent.mapper.EventMapper;
import com.bt.itsevent.service.EventVisitService;
import com.bt.itsevent.service.EventWorkOrderProgressService;
import com.bt.itsevent.service.EventWorkOrderService;
import com.google.gson.Gson;

/**
 * 
 * @Description: 排障工单处理类
 * @author: NingYiQiang
 * @date: 2021年10月11日 上午10:49:01
 */
@RestController
@RequestMapping("workorder")
public class WorkOrderController {
	@Autowired
	private EventWorkOrderService eventWorkOrderService;
	@Autowired
	private EventWorkOrderProgressService eventWorkOrderProgressService;
	@Autowired
	private EventVisitService eventVisitService;
	@Autowired
	EventMapper eventMapper;

	/**
	 * 排障工单信息管理
	 */
	@Login
	@PostMapping("pageEventWorkOrder")
	public Object pageEventWorkOrder(@Valid PageDTO pageDTO, @RequestBody EventWorkOrderDTO eventWorkOrderDTO) {
		PageVO vo = new PageVO(eventWorkOrderService.page(eventWorkOrderDTO, pageDTO));
		return vo;
	}

	@Login
	@PostMapping("addEventWorkOrder")
	public ResponseVO addEventWorkOrder(@Valid @RequestBody EventWorkOrderDTO eventWorkOrderDTO, BindingResult result) {
		ValidUtils.error(result);
		boolean ret = eventWorkOrderService.add(eventWorkOrderDTO);
		return new ResponseVO(ret);
	}

	@Login
	@PostMapping("updateEventWorkOrder")
	public ResponseVO updateEventWorkOrder(@Valid @RequestBody EventWorkOrderDTO eventWorkOrderDTO,
			BindingResult result) {
		ValidUtils.error(result);
		boolean ret = eventWorkOrderService.update(eventWorkOrderDTO);
		return new ResponseVO(ret);
	}

	@Login
	@PostMapping("deleteEventWorkOrder")
	public ResponseVO deleteEventWorkOrder(@RequestBody EventWorkOrderDTO eventWorkOrderDTO) {
		if (eventWorkOrderDTO == null || eventWorkOrderDTO.getId() == null) {
			return new ResponseVO("删除信息不能为空", 400);
		}
		boolean ret = eventWorkOrderService.delete(eventWorkOrderDTO);
		return new ResponseVO(ret);
	}

	@Login
	@PostMapping("/batchEventWorkOrder")
	public ResponseVO batchEventWorkOrder(@RequestBody IdStringBatchDTO idStringBatch) {
		if (idStringBatch.getIds() == null) {
			return new ResponseVO("删除信息不能为空", 400);
		}
		boolean ret = eventWorkOrderService.batchDelete(idStringBatch);
		return new ResponseVO(ret);
	}

	@Login
	@PostMapping("/exportEventWorkOrder")
	public ResponseVO exportEventWorkOrder(@RequestBody EventWorkOrderDTO eventWorkOrderDTO, HttpServletRequest request,
			HttpServletResponse response) {
		boolean flag = eventWorkOrderService.exportExcel(eventWorkOrderDTO, request, response);
		return new ResponseVO(flag);
	}

	/**
	 * 工单进展信息管理
	 */
	@Login
	@PostMapping("selectWorkOrderProgress")
	public Object selectWorkOrderProgress(@RequestBody EventWorkOrderProgressDTO workOrderProgressDTO) {
		List<EventWorkOrderProgressVO> vo = eventWorkOrderProgressService.selectWorkOrderProgress(workOrderProgressDTO);
		return vo;
	}

	// 新增排障工单进展单同时要新增一条事件进展信息
	@Login
	@PostMapping("addWorkOrderProgress")
	public ResponseVO addWorkOrderProgress(@Valid @RequestBody EventWorkOrderProgressDTO dto, BindingResult result,
			HttpServletRequest request) {
		ValidUtils.error(result);
		int ret = eventWorkOrderProgressService.add(dto, (String) request.getAttribute("userId"));
		return new ResponseVO(ret);

	}

	@Login
	@PostMapping("updateWorkOrderProgress")
	public ResponseVO updateWorkOrderProgress(@Valid @RequestBody EventWorkOrderProgressDTO dto, BindingResult result) {
		ValidUtils.error(result);
		boolean ret = eventWorkOrderProgressService.update(dto);
		return new ResponseVO(ret);
	}

	@Login
	@PostMapping("deleteWorkOrderProgress")
	public ResponseVO deleteWorkOrderProgress(@Valid @RequestBody IdStringDTO dto, BindingResult result) {
		ValidUtils.error(result);
		boolean ret = eventWorkOrderProgressService.delete(dto);
		return new ResponseVO(ret);
	}

	@Login
	@PostMapping("deleteProgressList")
	public ResponseVO deleteProgressList(@Valid @RequestBody IdStringDTO dto, BindingResult result) {
		ValidUtils.error(result);
		boolean ret = eventWorkOrderProgressService.deleteProgress(dto);
		return new ResponseVO(ret);
	}

	// 附件上传
	@Login
	@RequestMapping("ossUpload")
	public void ossUpload(AttachDTO dto, HttpServletRequest request, HttpServletResponse response) {
		Map<String, String> map = eventWorkOrderProgressService.ossUpload(dto);
		try {
			OssUtils.response(request, response, new Gson().toJson(map));
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	// 单个保存返回
	@Login
	@RequestMapping("ossCallback")
	public void ossCallback(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
//		String progressId = request.getParameter("progressId");
//		String attachType = request.getParameter("attachType");
		OssUtils.response(request, response, new Gson().toJson(eventWorkOrderProgressService.ossCallback(request)));
	}

	// 删除附件
	@Login
	@RequestMapping("ossDelete")
	public ResponseVO ossDelete(EventWorkOrderAttachDTO dto) {
		boolean ret = eventWorkOrderProgressService.ossDelete(dto);
		return new ResponseVO(ret);
	}

	// 排障工单回访管理
	@Login
	@PostMapping("pageEventVisit")
	public Object pageEventVisit(@Valid PageDTO pageDTO, @RequestBody EventVisitDTO dto) {
		PageVO vo = new PageVO(eventVisitService.page(dto, pageDTO));
		return vo;
	}

	@Login
	@CheckAuthz(hasPermissions = "system:workorder:addEventVisit")
	@PostMapping("addEventVisit")
	public ResponseVO addEventVisit(@Valid @RequestBody EventVisitDTO dto, BindingResult result) {
		ValidUtils.error(result);
		String eventId = dto.getEventId();
		if (StringUtils.isBlank(eventId)) {
			return new ResponseVO("所属事件参数不存在，不能新增！", 400);
		}
		if (eventVisitService.checkEntiryExit(eventId)) {
			return new ResponseVO("当前回访信息已经存在，不能新增！", 400);
		}
		boolean ret = eventVisitService.addEventVisit(dto);
		return new ResponseVO(ret);
	}

	@Login
	@PostMapping("updateEventVisit")
	public ResponseVO updateEventVisit(@Valid @RequestBody EventVisitDTO dto, BindingResult result) {
		ValidUtils.error(result);
		boolean ret = eventVisitService.update(dto);
		return new ResponseVO(ret);
	}

	@Login
	@PostMapping("deleteEventVisit")
	public ResponseVO deleteEventVisit(@RequestBody EventVisitDTO dto) {
		if (dto == null || dto.getId() == null) {
			return new ResponseVO("删除信息不能为空", 400);
		}
		boolean ret = eventVisitService.delete(dto);
		return new ResponseVO(ret);
	}

	@Login
	@PostMapping("getEventVisit")
	public Object getEventVisit(@RequestBody EventVisitDTO dto) {
		return eventVisitService.getEventVisit(dto.getId());
	}

	@Login
	@PostMapping("getEventVisitByEventId")
	public Object getEventVisitByEventId(@RequestBody EventVisitDTO dto, HttpServletRequest request) {
		String userName = (String) request.getAttribute("userName");
		return eventVisitService.getEventVisitByEventId(dto.getEventId(), userName);
	}

	@Login
	@PostMapping("getEventWorkOrderByEventId")
	public Object getEventWorkOrderByEventId(@RequestBody EventWorkOrderDTO eventWorkOrderDTO) {
		return eventWorkOrderService.getEventWorkOrderByEventId(eventWorkOrderDTO.getEventId());
	}

	/**
	 * @描述 排障出车单，参数progressStep:1-出车单；2-现场作业单，3-目的地单；4-收车单
	 * @return
	 */
	@Login
	@PostMapping("progressCount")
	public Object progressCount(@RequestBody EventWorkOrderProgressDTO dto) {
		return eventWorkOrderService.progressCount(dto);
	}
	
	/**
	 * @描述 提交公众号上的事件用户评价
	 */
	@Login
	@PostMapping("addEventVisitForGzh")
	public ResponseVO addEventVisitForGzh(@Valid @RequestBody EventVisitForGzhDTO dto, BindingResult result) {
		ValidUtils.error(result);
		String eventId = dto.getEventId();
		if (eventVisitService.checkEntiryExit(eventId)) {
			return new ResponseVO("已评价过，请勿重复评价！", 400);
		}
		boolean ret = eventVisitService.addEventVisitForGzh(dto);
		return new ResponseVO(ret);
	}

	/**
	 * @描述 查询公众号上的事件用户评价
	 */
	@Login
	@PostMapping("selectEventVisitForGzh")
	public Object selectEventVisitForGzh(@Valid @RequestBody IdStringDTO dto, BindingResult result) {
		ValidUtils.error(result);
		return eventVisitService.getEventVisit(dto.getId());
	}
}
