package com.bt.itsevent.service;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.bt.itscore.domain.dto.DictItemDTO;
import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.vo.DictItemVO;
import com.bt.itscore.domain.vo.PageVO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itscore.domain.vo.UserSimpleVO;
import com.bt.itscore.enums.OrgSourceIdEnum;
import com.bt.itscore.utils.TimeUtils;
import com.bt.itsevent.domain.dto.EventBriefDTO;
import com.bt.itsevent.domain.dto.EventLogQueryDTO;
import com.bt.itsevent.domain.dto.ProgressDTO;
import com.bt.itsevent.domain.dto.ProgressSceneDTO;
import com.bt.itsevent.domain.vo.EventDetailVO;
import com.bt.itsevent.domain.vo.EventLogVO;
import com.bt.itsevent.domain.vo.EventTypeRescueVO;
import com.bt.itsevent.domain.vo.ProgressAttachVO;
import com.bt.itsevent.domain.vo.ProgressSceneVO;
import com.bt.itsevent.domain.vo.ProgressVO;
import com.bt.itsevent.feign.FeignClient;
import com.bt.itsevent.mapper.EventLogMapper;
import com.bt.itsevent.mapper.EventMapper;
import com.bt.itsevent.mapper.EventTypeMapper;
import com.bt.itsevent.mapper.ProgressMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
@Service("eventLogService")
public class EventLogService {
	@Autowired
	private EventLogMapper eventLogMapper;
	@Autowired
	private EventMapper eventMapper;
	@Autowired
	private EventTypeMapper eventTypeMapper;
	@Autowired
	private ProgressMapper progressMapper;
    @Autowired
    private FeignClient feignClient;

	public PageVO page(PageDTO pageDTO, EventLogQueryDTO dto) {
		PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
		Long startTime = dto.getStartTime();
		Long endTime = dto.getEndTime();
		if (startTime != null) {
			dto.setCreateStartTime(TimeUtils.getTimeString(TimeUtils.FULL_TIME, startTime * 1000));
		}
		if (endTime != null) {
			dto.setCreateEndTime(TimeUtils.getTimeString(TimeUtils.FULL_TIME, endTime * 1000));
		}
		List<EventLogVO> list = eventLogMapper.selectList(dto);
		PageInfo<EventLogVO> pageInfo = new PageInfo<>(list);
		return new PageVO(pageInfo);
	}

	public Object selectEventBriefByStage(EventBriefDTO dto) {
		Integer eventStage = dto.getEventStage();
		String eventId = dto.getEventId();
		IdStringDTO eventIdDTO = new IdStringDTO(eventId);
		EventDetailVO eventDetail = eventMapper.selectEmerRescueByEventId(eventIdDTO);
		if (eventStage.intValue() == 1) { // 接报
	    	Integer reportSourceKey = eventDetail.getReportSourceKey();
	    	String reportSource = eventDetail.getReportSource();
	    	String reportSourceStr = "";
	    	Integer eventFourType = eventDetail.getEventFourType();
	    	if (reportSourceKey != null) {
	    		if (reportSourceKey == 99) {
	    			reportSourceStr = reportSource;
	    		} else {
	    			reportSourceStr = this.getDictItemName(123, "事件管理-接报来源", String.valueOf(reportSourceKey));
	    		}
	    	}
	    	String eventTypeName = "";
	    	Integer lastLevelEventType = eventFourType;
	    	
	    	if (eventFourType == null) {
	    		lastLevelEventType = eventDetail.getEventThreeType();
	    	}
	    	EventTypeRescueVO eventTypeVO = eventTypeMapper.getEventTypeById(new IdStringDTO(lastLevelEventType + ""));
	    	if (eventTypeVO != null) {
	    		eventTypeName = eventTypeVO.getName();
	    	}
	    	return new ResponseVO(OrgSourceIdEnum.getShortName(eventDetail.getSourceId()) + "公司有一条" + reportSourceStr + "上报的" + eventTypeName + "事件", 1);
		} else if (eventStage.intValue() == 2) { // 事件分发
			ProgressVO initReportVO = progressMapper.selectInitReport(eventIdDTO);
			if (initReportVO != null) {
				String createUserId = initReportVO.getCreateUserId();
				UserSimpleVO userVO = feignClient.selectByUserId(createUserId);
				return new ResponseVO(userVO.getUserName() + "已分发事件并发送初报", 1);
			}
		} else if (eventStage.intValue() == 3) { // 确认
			ProgressDTO progressDTO = new ProgressDTO();
			progressDTO.setEventId(eventId);
			progressDTO.setCardType(0);
			progressDTO.setCardPass(5);
			List<ProgressVO> progressList = progressMapper.selectListByCardTypeAndCardPass(progressDTO);
			if (!CollectionUtils.isEmpty(progressList)) {
				String eventBrief = "";
				for (ProgressVO progressVO : progressList) {
					eventBrief += progressVO.getCreateUserName() + "、";
				}
				eventBrief = eventBrief.substring(0, eventBrief.length()-1);
				eventBrief += "确认了事件初报";
				return new ResponseVO(eventBrief, 1);
			}
		} else if (eventStage.intValue() == 4) { // 出发
			ProgressDTO progressDTO = new ProgressDTO();
			progressDTO.setEventId(eventId);
			progressDTO.setCardType(0);
			progressDTO.setCardPass(15);
			List<ProgressVO> progressList = progressMapper.selectListByCardTypeAndCardPass(progressDTO);
			if (!CollectionUtils.isEmpty(progressList)) {
				String eventBrief = "";
				for (ProgressVO progressVO : progressList) {
					eventBrief += progressVO.getCreateUserName() + "、";
				}
				eventBrief = eventBrief.substring(0, eventBrief.length()-1);
				eventBrief += "已出发";
				return new ResponseVO(eventBrief, 1);
			}
		} else if (eventStage.intValue() == 5) { // 到达
			ProgressDTO progressDTO = new ProgressDTO();
			progressDTO.setEventId(eventId);
			progressDTO.setCardType(0);
			progressDTO.setCardPass(20);
			List<ProgressVO> progressList = progressMapper.selectListByCardTypeAndCardPass(progressDTO);
			if (!CollectionUtils.isEmpty(progressList)) {
				String eventBrief = "";
				for (ProgressVO progressVO : progressList) {
					eventBrief += progressVO.getCreateUserName() + "、";
				}
				eventBrief = eventBrief.substring(0, eventBrief.length()-1);
				eventBrief += "已到达";
				return new ResponseVO(eventBrief, 1);
			}
		} else if (eventStage.intValue() == 6) { // 警戒疏散交通管制
			String eventBrief = getSceneBrief(eventId, 10);
			if (StringUtils.isNoneBlank(eventBrief)) {
				return new ResponseVO(eventBrief + "正进行警戒疏散交通管制", 1);
			}
		} else if (eventStage.intValue() == 7) { // 人员搜救医疗救治
			String eventBrief = getSceneBrief(eventId, 20);
			if (StringUtils.isNoneBlank(eventBrief)) {
				return new ResponseVO(eventBrief + "正进行人员搜救医疗救治", 1);
			}
		} else if (eventStage.intValue() == 8) { // 抢险救援
			String eventBrief = getSceneBrief(eventId, 30);
			if (StringUtils.isNoneBlank(eventBrief)) {
				return new ResponseVO(eventBrief + "正进行抢险救援", 1);
			}
		} else if (eventStage.intValue() == 9) { // 勘查定损调查处置
			String eventBrief = getSceneBrief(eventId, 40);
			if (StringUtils.isNoneBlank(eventBrief)) {
				return new ResponseVO(eventBrief + "正进行勘查定损调查处置", 1);
			}
		} else if (eventStage.intValue() == 10) { // 清障救援路面恢复
			String eventBrief = getSceneBrief(eventId, 50);
			if (StringUtils.isNoneBlank(eventBrief)) {
				return new ResponseVO(eventBrief + "正进行清障救援路面恢复", 1);
			}
		} else if (eventStage.intValue() == 11) { // 已离开
			ProgressDTO progressDTO = new ProgressDTO();
			progressDTO.setEventId(eventId);
			progressDTO.setCardType(0);
			progressDTO.setCardPass(30);
			List<ProgressVO> progressList = progressMapper.selectListByCardTypeAndCardPass(progressDTO);
			if (!CollectionUtils.isEmpty(progressList)) {
				String eventBrief = "";
				for (ProgressVO progressVO : progressList) {
					eventBrief += progressVO.getCreateUserName() + "、";
				}
				eventBrief = eventBrief.substring(0, eventBrief.length()-1);
				eventBrief += "已离开";
				return new ResponseVO(eventBrief, 1);
			}
		} else if (eventStage.intValue() == 12) { // 已完结
			Integer dealStatus = eventDetail.getDealStatus();
			if (dealStatus != null && dealStatus.intValue() == 100) {
				ProgressDTO progressDTO = new ProgressDTO();
				progressDTO.setEventId(eventId);
				progressDTO.setCardType(0);
				progressDTO.setCardPass(100);
				List<ProgressVO> progressList = progressMapper.selectListByCardTypeAndCardPass(progressDTO);
				if (!CollectionUtils.isEmpty(progressList)) {
					String eventBrief = "";
					for (ProgressVO progressVO : progressList) {
						eventBrief += progressVO.getCreateUserName() + "、";
					}
					eventBrief = eventBrief.substring(0, eventBrief.length()-1);
					eventBrief += "已完结事件";
					return new ResponseVO(eventBrief, 1);
				}
			}
		}
		
		return new ResponseVO("", 0);
	}
	
	private String getSceneBrief(String eventId, int step) {
		ProgressSceneDTO progressSceneDTO = new ProgressSceneDTO();
		progressSceneDTO.setId(eventId);
		progressSceneDTO.setStep(step);
		List<ProgressSceneVO> list = progressMapper.selectSceneByStep(progressSceneDTO);
		if (!CollectionUtils.isEmpty(list)) {
			ProgressSceneVO progressSceneVO = list.get(0);
			List<ProgressAttachVO> attachs = progressSceneVO.getAttachs();
			String userIds = ";";
			String eventBrief = "";
			if (!CollectionUtils.isEmpty(attachs)) {
				for (ProgressAttachVO progressAttachVO : attachs) {
					if (!userIds.contains(progressAttachVO.getUserId())) {
						userIds += progressAttachVO.getUserId() + ";";
						eventBrief += progressAttachVO.getUserName() + "、";
					}
				}
				eventBrief = eventBrief.substring(0, eventBrief.length()-1);
				return eventBrief;
			}
		}
		return "";
	}

    private String getDictItemName(Integer typeId, String typeName, String value) {
        String dictItemName = "";// 字典数据项名称
        if ("999".equals(value)) {
            return "";
        }
        DictItemDTO dto = new DictItemDTO();
        dto.setTypeId(typeId);
        dto.setTypeName(typeName);
        List<DictItemVO> itemList = feignClient.selectDictItem(dto);
        if (itemList.size() > 0) {
            for (DictItemVO dictItemVO : itemList) {
                if (dictItemVO.getValue().equals(value)) {
                    dictItemName = dictItemVO.getName();
                    break;
                }
            }
        }
        return dictItemName;
    }
}
