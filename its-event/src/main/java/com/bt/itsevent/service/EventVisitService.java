package com.bt.itsevent.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.bt.itscore.constants.SystemConstants;
import com.bt.itscore.domain.dto.CommandDTO;
import com.bt.itscore.domain.dto.Event96333SubmitDTO;
import com.bt.itscore.domain.dto.Event96333VisitDTO;
import com.bt.itscore.domain.dto.IdStringDTO;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.enums.ServiceTypeEnum;
import com.bt.itscore.enums.SourceIdEnum;
import com.bt.itscore.utils.TimeUtils;
import com.bt.itsevent.domain.dto.EventVisitDTO;
import com.bt.itsevent.domain.dto.EventVisitForGzhDTO;
import com.bt.itsevent.domain.dto.EventWorkOrderDTO;
import com.bt.itsevent.domain.vo.EventDetailVO;
import com.bt.itsevent.domain.vo.EventVisitVO;
import com.bt.itsevent.feign.ItsMsFeignClient;
import com.bt.itsevent.mapper.EventMapper;
import com.bt.itsevent.mapper.EventVisitMapper;
import com.bt.itsevent.mapper.EventWorkOrderMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;

@Service("eventVisitService")
public class EventVisitService {

    private final static Logger LOGGER = LoggerFactory.getLogger(EventVisitService.class);
    @Value("${event.rpc96333.finish}")
    private String eventRpc96333Finish;
    @Value("${event.96333.mode:new}")
    private String event96333Mode;
	@Autowired
	EventVisitMapper eventVisitMapper;
	@Autowired
	EventWorkOrderMapper eventWorkOrderMapper;
	@Autowired
	EventMapper eventMapper;
	@Autowired
    ItsMsFeignClient itsMsFeignClient;

	public PageInfo<EventVisitVO> page(EventVisitDTO dto, PageDTO pageDTO) {
		PageHelper.startPage(pageDTO.getPage(), pageDTO.getLimit());
		List<EventVisitVO> list = eventVisitMapper.selectList(dto);
		return new PageInfo<>(list);
	}

	public List<EventVisitVO> selectList(EventVisitDTO dto) {
		return eventVisitMapper.selectList(dto);
	}

	public EventVisitVO getEventVisit(String id) {
		return eventVisitMapper.getEventVisit(id);
	}

	public EventVisitVO getEventVisitByEventId(String eventId,String userName) {
		EventVisitVO eventVisitVO = eventVisitMapper.getEventVisitByEventId(eventId);
		if (eventVisitVO ==null) {
			eventVisitVO = new EventVisitVO();
			IdStringDTO ids = new IdStringDTO();
			ids.setId(eventId);
			EventDetailVO vo = eventMapper.selectEmerRescueByEventId(ids);
			if (vo != null) {
				eventVisitVO.setClient(vo.getReportMan());
				String reportManTel = vo.getReportManTel();
		        if(reportManTel != null) {
		        	vo.setReportManTel(reportManTel.trim());
		        }
				eventVisitVO.setTelephone(vo.getReportManTel());
				eventVisitVO.setClientPlate(vo.getCarPlate());
			}
			eventVisitVO.setEventId(eventId);
			eventVisitVO.setVisitMan(userName);
			eventVisitVO.setVisitTime(System.currentTimeMillis() / 1000);
		}
		return eventVisitVO;
	}

	/**
	 * @描述 事件回访
	 */
	public boolean addEventVisit(EventVisitDTO dto) {
		if("new".equals(event96333Mode)) {
			return add96333Visit(dto);
		} else {
			return add(dto);
		}
	}
	
	public boolean add(EventVisitDTO dto) {
		String eventId = dto.getEventId();
		IdStringDTO eventIdDTO = new IdStringDTO(eventId);
		long serverTime = System.currentTimeMillis() / 1000;

		EventDetailVO detailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
		Integer eventType = detailVO.getEventType();
		int type = eventType == null ? 0 : eventType;
		
		dto.setCreateTime(serverTime);
		dto.setId(UUID.randomUUID().toString());// 排障单ID
		boolean result = eventVisitMapper.add(dto) > 0;
		// 提交回访消息到96333
		Integer source = detailVO.getSource();
		int eventSource = source == null ? 0 : source;
		if (eventSource == 1) {// 96333事件
			LOGGER.info("提交96333开关:{},eventNo:{}", eventRpc96333Finish, detailVO.getEventNo());
			if (result && "1".equals(eventRpc96333Finish) && !detailVO.getEventNo().contains("-")) {
				// 处理96333事件，发送一个指令到command交换器（8.134）
				CommandDTO commandDTO = new CommandDTO();
				commandDTO.setSourceId("" + detailVO.getSourceId());
				commandDTO.setUuid(UUID.randomUUID().toString());
				commandDTO.setType("Event96333Visit");
				// 事件编号eventNo，96333中的id(busiId)，处理结果dealResult
				Event96333SubmitDTO submitDTO = new Event96333SubmitDTO();
				submitDTO.setBusiId(detailVO.getBusiId());
				submitDTO.setDealResult(dto.getSuggestion());
				submitDTO.setEventNo(detailVO.getEventNo());
				submitDTO.setScore(dto.getAppraisal());
				submitDTO.setName(dto.getClient());
				submitDTO.setTel(dto.getTelephone());
				commandDTO.setParams(new Gson().toJson(submitDTO));
				itsMsFeignClient.produce(commandDTO);
			}
		}

		if (type == 1 && result) {// 更新排障工单和事件回访状态
			EventWorkOrderDTO eventWorkOrderDTO = new EventWorkOrderDTO();
			eventWorkOrderDTO.setEventId(eventId);
			eventWorkOrderDTO.setRevisit(2);// 设置为已经回访
			eventWorkOrderMapper.update(eventWorkOrderDTO);
			Map<String, Object> map = new HashMap<>();
			map.put("id", eventId);
			map.put("appraiseStatus", 2);
			eventMapper.updateEventByOtherStatus(map);
		}

		return result;
	}

	/**
	 * @描述 回访结果到96333（千方对接使用）
	 */
	public boolean add96333Visit(EventVisitDTO dto) {
		String eventId = dto.getEventId();
		IdStringDTO eventIdDTO = new IdStringDTO(eventId);
		long serverTime = System.currentTimeMillis() / 1000;

		EventDetailVO detailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
		Integer eventType = detailVO.getEventType();
		int type = eventType == null ? 0 : eventType;
		String eventNo = detailVO.getEventNo();
		dto.setCreateTime(serverTime);
		dto.setId(UUID.randomUUID().toString());// 排障单ID
		boolean result = eventVisitMapper.add(dto) > 0;
		// 新增一条需要同步状态的记录event_96333_sync
		Map<String, Object> map = new HashMap<>();
        map.put("eventId", eventId);
        map.put("eventNo", eventNo);
        int row = eventMapper.selectEvent96333ResultCode(eventIdDTO);
        if(row == 0) {
        	eventMapper.addEvent96333ResultCode(map);
        }
		// 提交回访消息到96333
		Integer source = detailVO.getSource();
		int eventSource = source == null ? 0 : source;
		if (eventSource == 1) {// 96333事件
			LOGGER.info("提交96333开关:{},eventNo:{}", eventRpc96333Finish, eventNo);
			if (result && "1".equals(eventRpc96333Finish) && !eventNo.contains("-")) {
				// 处理96333事件，发送一个指令到command交换器（8.134）
				CommandDTO commandDTO = new CommandDTO();
				commandDTO.setSourceId("" + SourceIdEnum.XFZ_99.getIndex());//新发展来源
                commandDTO.setUuid(UUID.randomUUID().toString());
                commandDTO.setType(SystemConstants.EVENT_96333_RETURN_VISIT_TYPE);
				// 事件编号eventNo，96333中的id(busiId)，处理结果dealResult
				Event96333VisitDTO submitDTO = new Event96333VisitDTO();
				submitDTO.setCheckTime(TimeUtils.getTimeString(TimeUtils.DATE_TIME_7));
				submitDTO.setCheckOperator(dto.getVisitMan());
				submitDTO.setCheckDepartment("新发展客服中心");
				submitDTO.setCheckContent(dto.getSuggestion());
				submitDTO.setEventNo(eventNo);
				submitDTO.setScord(dto.getAppraisal());
				submitDTO.setSatId(eventNo.substring(8, 16));
				submitDTO.setName(dto.getClient());
				submitDTO.setPhone(dto.getTelephone());
				submitDTO.setSourceId(detailVO.getSourceId());
				String serviceType = ServiceTypeEnum.DD.getValue();
				if(eventNo.startsWith(ServiceTypeEnum.DD.getName())) {
					serviceType = ServiceTypeEnum.DD.getValue();
				} else if(eventNo.startsWith(ServiceTypeEnum.TS.getName())) {
					serviceType = ServiceTypeEnum.TS.getValue();
				} else if(eventNo.startsWith(ServiceTypeEnum.JY.getName())) {
					serviceType = ServiceTypeEnum.JY.getValue();
				}
				submitDTO.setServiceType(serviceType);
				commandDTO.setParams(new Gson().toJson(submitDTO));
				itsMsFeignClient.produce(commandDTO);
			}
		}

		if (type == 1 && result) {// 更新排障工单和事件回访状态
			EventWorkOrderDTO eventWorkOrderDTO = new EventWorkOrderDTO();
			eventWorkOrderDTO.setEventId(eventId);
			eventWorkOrderDTO.setRevisit(2);// 设置为已经回访
			eventWorkOrderMapper.update(eventWorkOrderDTO);
			map.put("id", eventId);
			map.put("appraiseStatus", 2);
			eventMapper.updateEventByOtherStatus(map);
		}

		return result;
	}

	public boolean update(EventVisitDTO dto) {
		boolean result = eventVisitMapper.update(dto) > 0;
		if (result) {
			String eventId = dto.getEventId();
			EventWorkOrderDTO eventWorkOrderDTO = new EventWorkOrderDTO();
			eventWorkOrderDTO.setEventId(eventId);
			eventWorkOrderDTO.setRevisit(2);// 设置为已经回访
			eventWorkOrderMapper.update(eventWorkOrderDTO);
			Map<String, Object> map = new HashMap<>();
			map.put("id", eventId);
			map.put("appraiseStatus", 2);
			eventMapper.updateEventByOtherStatus(map);
		}

		return result;
	}

	public boolean delete(EventVisitDTO dto) {
		boolean result = eventVisitMapper.delete(dto) > 0;
		if (result) {// 更新工单回访状态
			String eventId = dto.getEventId();
			EventWorkOrderDTO eventWorkOrderDTO = new EventWorkOrderDTO();
			eventWorkOrderDTO.setEventId(eventId);
			eventWorkOrderDTO.setRevisit(1);// 设置为还未回访
			eventWorkOrderMapper.update(eventWorkOrderDTO);
			Map<String, Object> map = new HashMap<>();
			map.put("id", eventId);
			map.put("appraiseStatus", 1);// 事件评价
			eventMapper.updateEventByOtherStatus(map);
		}

		return result;
	}

	public boolean checkEntiryExit(String eventId) {
		int count = eventVisitMapper.totalcount(eventId);
		if (count > 0) {
			return true;
		} else {
			return false;
		}
	}

	/** 公众号上的事件用户评价 **/
	public boolean addEventVisitForGzh(EventVisitForGzhDTO dto) {
		String eventId = dto.getEventId();
		IdStringDTO eventIdDTO = new IdStringDTO(eventId);
		long serverTime = System.currentTimeMillis() / 1000;

		EventDetailVO detailVO = eventMapper.selectEmerRescueByEventId(eventIdDTO);
		Integer eventType = detailVO.getEventType();
		int type = eventType == null ? 0 : eventType;
		dto.setClient(detailVO.getReportMan());
		dto.setTelephone(detailVO.getReportManTel());
		dto.setClientPlate(detailVO.getCarPlate());
		dto.setVisitTime(serverTime);
		dto.setVisitMan("公众号");
		dto.setCreateTime(serverTime);
		dto.setId(UUID.randomUUID().toString());// 排障单ID
		boolean result = eventVisitMapper.addEventVisitForGzh(dto) > 0;

		if (type == 1 && result) {// 更新排障工单和事件回访状态
			EventWorkOrderDTO eventWorkOrderDTO = new EventWorkOrderDTO();
			eventWorkOrderDTO.setEventId(eventId);
			eventWorkOrderDTO.setRevisit(2);// 设置为已经回访
			eventWorkOrderMapper.update(eventWorkOrderDTO);
			Map<String, Object> map = new HashMap<>();
			map.put("id", eventId);
			map.put("appraiseStatus", 2);
			eventMapper.updateEventByOtherStatus(map);
		}

		return result;
	}

}
