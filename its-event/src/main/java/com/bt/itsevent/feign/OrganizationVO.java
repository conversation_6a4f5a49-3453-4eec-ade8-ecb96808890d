package com.bt.itsevent.feign;

import java.util.ArrayList;
import java.util.List;

import com.bt.itsevent.domain.vo.ParentRoadVO;

public class OrganizationVO {
	private String orgId;// 组织机构id
	private String orgName;// 组织机构名称
	private String shortName;// 组织机构简称
	private Integer orgType;// 0部门;1公司
	private String companyId;// 公司id
	private String companyName;// 公司名称
	private String companyShortName;// 公司简称
	private String orgTel;// 机构电话
	private String leader;// 机构负责人
	private Integer use;// 0停用；1正常
	private String pid;// 父级orgId
	private Integer sort;// 排序号
	private Long createTime;// 记录生成时间
	private List<OrganizationVO> children = new ArrayList<OrganizationVO>();
	private List<ParentRoadVO> parentRoads = new ArrayList<ParentRoadVO>();
	private List<RoadVO> roads = new ArrayList<>();

	public String getCompanyId() {
		return companyId;
	}

	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getShortName() {
		return shortName;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public Integer getOrgType() {
		return orgType;
	}

	public void setOrgType(Integer orgType) {
		this.orgType = orgType;
	}

	public String getOrgTel() {
		return orgTel;
	}

	public void setOrgTel(String orgTel) {
		this.orgTel = orgTel;
	}

	public String getLeader() {
		return leader;
	}

	public void setLeader(String leader) {
		this.leader = leader;
	}

	public Integer getUse() {
		return use;
	}

	public void setUse(Integer use) {
		this.use = use;
	}

	public String getPid() {
		return pid;
	}

	public void setPid(String pid) {
		this.pid = pid;
	}

	public Long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public List<OrganizationVO> getChildren() {
		return children;
	}

	public void setChildren(List<OrganizationVO> children) {
		this.children = children;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public String getCompanyShortName() {
		return companyShortName;
	}

	public void setCompanyShortName(String companyShortName) {
		this.companyShortName = companyShortName;
	}

	public List<ParentRoadVO> getParentRoads() {
		return parentRoads;
	}

	public void setParentRoads(List<ParentRoadVO> parentRoads) {
		this.parentRoads = parentRoads;
	}

	public List<RoadVO> getRoads() {
		return roads;
	}

	public void setRoads(List<RoadVO> roads) {
		this.roads = roads;
	}
}
