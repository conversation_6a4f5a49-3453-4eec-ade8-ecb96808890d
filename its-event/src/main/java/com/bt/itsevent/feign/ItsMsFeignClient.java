package com.bt.itsevent.feign;

import com.bt.itscore.domain.dto.CommandDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "its-ms")
public interface ItsMsFeignClient {
	@PostMapping("/ms/produce")
	public void produce(@RequestBody CommandDTO commandDTO);
}
