package com.bt.itsemer.domain.vo;

import java.util.List;

/**
* <AUTHOR>
* @date 2021年10月12日 下午5:05:46
* @Description 应急预案资源类型展示类
 */
public class EmerPlanResourceTypeVO {
	private Integer emerResourceTypeId;
	private String firstTypeName;//应急资源一级类型名称
	private List<EmerPlanResourceTypeVO> emerPlanResourceTypes;//应急资源二级类型
	private String secondTypeName;//应急资源二级类型名称
	private Integer emerPlanFlag;//1-已关联预案，0-未关联
	private Integer recommendNo;//建议数量
	private String unit;//单位
	public Integer getEmerResourceTypeId() {
		return emerResourceTypeId;
	}
	public void setEmerResourceTypeId(Integer emerResourceTypeId) {
		this.emerResourceTypeId = emerResourceTypeId;
	}
	public String getFirstTypeName() {
		return firstTypeName;
	}
	public void setFirstTypeName(String firstTypeName) {
		this.firstTypeName = firstTypeName;
	}
	public String getSecondTypeName() {
		return secondTypeName;
	}
	public void setSecondTypeName(String secondTypeName) {
		this.secondTypeName = secondTypeName;
	}
	public List<EmerPlanResourceTypeVO> getEmerPlanResourceTypes() {
		return emerPlanResourceTypes;
	}
	public void setEmerPlanResourceTypes(List<EmerPlanResourceTypeVO> emerPlanResourceTypes) {
		this.emerPlanResourceTypes = emerPlanResourceTypes;
	}
	public Integer getEmerPlanFlag() {
		return emerPlanFlag;
	}
	public void setEmerPlanFlag(Integer emerPlanFlag) {
		this.emerPlanFlag = emerPlanFlag;
	}
	public Integer getRecommendNo() {
		return recommendNo;
	}
	public void setRecommendNo(Integer recommendNo) {
		this.recommendNo = recommendNo;
	}
	public String getUnit() {
		return unit;
	}
	public void setUnit(String unit) {
		this.unit = unit;
	}
}
