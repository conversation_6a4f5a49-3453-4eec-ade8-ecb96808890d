package com.bt.itsemer.domain.dto;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class EmerResourceDTO {
	private String id;//varchar(36) NOT NULL COMMENT '应急资源ID',
	@NotBlank(message = "所属应急仓库设施编号不能为空")
	private String whId;//varchar(36) NOT NULL COMMENT '所属应急仓库编号，设施facility_no',
	@NotBlank(message = "应急资源名称不能为空")
	private String resourceName;//varchar(50) NOT NULL COMMENT '应急资源名称',
	private String brand;//varchar(50) DEFAULT '-' COMMENT '品牌',
	private String modelNum;//varchar(50) DEFAULT '-' COMMENT '型号',
	private String plate;//varchar(20) DEFAULT '-' COMMENT '车辆资源时填写的车牌信息',
	@NotNull(message = "一级类别编号不能为空")
	private Integer firstType;//int(11) NOT NULL COMMENT '一级类别',
	@NotNull(message = "二级类别编号不能为空")
	private Integer secondType;//int(11) NOT NULL COMMENT '二级类别',
	@NotBlank(message = "单位不能为空")
	private String unit;//varchar(20) NOT NULL COMMENT '单位',
	@Min(value = 0, message = "数量不能小于0")
	private Integer amount;//int(11) NOT NULL DEFAULT '0' COMMENT '数量',
	private String param;//text COMMENT '参数',
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getWhId() {
		return whId;
	}
	public void setWhId(String whId) {
		this.whId = whId;
	}
	public String getResourceName() {
		return resourceName;
	}
	public void setResourceName(String resourceName) {
		this.resourceName = resourceName;
	}
	public String getBrand() {
		return brand;
	}
	public void setBrand(String brand) {
		this.brand = brand;
	}
	public String getModelNum() {
		return modelNum;
	}
	public void setModelNum(String modelNum) {
		this.modelNum = modelNum;
	}
	public String getPlate() {
		return plate;
	}
	public void setPlate(String plate) {
		this.plate = plate;
	}
	public Integer getFirstType() {
		return firstType;
	}
	public void setFirstType(Integer firstType) {
		this.firstType = firstType;
	}
	public Integer getSecondType() {
		return secondType;
	}
	public void setSecondType(Integer secondType) {
		this.secondType = secondType;
	}
	public String getUnit() {
		return unit;
	}
	public void setUnit(String unit) {
		this.unit = unit;
	}
	public Integer getAmount() {
		return amount;
	}
	public void setAmount(Integer amount) {
		this.amount = amount;
	}
	public String getParam() {
		return param;
	}
	public void setParam(String param) {
		this.param = param;
	}
}
