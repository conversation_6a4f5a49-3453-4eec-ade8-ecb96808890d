package com.bt.itsemer.domain.vo;

import java.util.List;

import com.bt.itsemer.domain.entity.User;

public class EmerGroupMatchVO {
	private String emerGroupId;//应急小组编号
	private String emerGroupName;//应急小组名称
	private Integer forceRemind;//1-强提醒，0-不需要强提醒
	private Integer smsRemind;//1-短信提醒，0-不需要短信提醒
	private List<User> users;//应急人员
	private Integer level;//预案等级值，普通-10，I级-1，II级-2，III级-3，IV级-4
	public String getEmerGroupId() {
		return emerGroupId;
	}
	public void setEmerGroupId(String emerGroupId) {
		this.emerGroupId = emerGroupId;
	}
	public String getEmerGroupName() {
		return emerGroupName;
	}
	public void setEmerGroupName(String emerGroupName) {
		this.emerGroupName = emerGroupName;
	}
	public Integer getForceRemind() {
		return forceRemind;
	}
	public void setForceRemind(Integer forceRemind) {
		this.forceRemind = forceRemind;
	}
	public Integer getSmsRemind() {
		return smsRemind;
	}
	public void setSmsRemind(Integer smsRemind) {
		this.smsRemind = smsRemind;
	}
	public List<User> getUsers() {
		return users;
	}
	public void setUsers(List<User> users) {
		this.users = users;
	}
	public Integer getLevel() {
		return level;
	}
	public void setLevel(Integer level) {
		this.level = level;
	}
	
}
