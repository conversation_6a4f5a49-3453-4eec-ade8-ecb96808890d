package com.bt.itsemer.domain.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class UpdateEmerRoleDTO {
	@NotBlank(message = "应急角色编号不能为空")
	private String id;//主键
	@NotBlank(message = "应急角色名称不能为空")
	private String roleName;//应急角色名称
	private String content;//工作内容
	private Integer value;//值
	private Integer sort;//排序
	private Integer delStatus;//0-启动，1-禁用
	@NotNull(message = "公司ID不能为空")
	private List<String> orgIds;//公司ID
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getRoleName() {
		return roleName;
	}
	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public Integer getSort() {
		return sort;
	}
	public Integer getValue() {
		return value;
	}
	public void setValue(Integer value) {
		this.value = value;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public Integer getDelStatus() {
		return delStatus;
	}
	public void setDelStatus(Integer delStatus) {
		this.delStatus = delStatus;
	}
	public List<String> getOrgIds() {
		return orgIds;
	}
	public void setOrgIds(List<String> orgIds) {
		this.orgIds = orgIds;
	}
	
}
