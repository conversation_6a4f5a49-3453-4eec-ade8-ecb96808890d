package com.bt.itsjob.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.bt.itscore.utils.GsonUtils;
import com.bt.itscore.utils.HttpClientUtils;
import com.bt.itsjob.domain.dto.uav.DeviceDTO;
import com.bt.itsjob.domain.dto.uav.DeviceReturnDTO;
import com.bt.itsjob.domain.dto.uav.LoginReturnDTO;
import com.bt.itsjob.mapper.DeviceMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

/**
 * <AUTHOR>
 * @date 2025年3月8日 上午9:14:36
 * @Description 无人机设备同步
 */
@JobHandler(value = "deviceUavSyncHandler")
@Component
public class DeviceUavSyncHandler extends IJobHandler {

	@Value("${uav.host:http://***********:6789}")
	private String uavHost;
	@Value("${uav.loginurl:/system/api/v1/login}")
	private String uavLoginUrl;// 无人机系统登录URL
	@Value("${uav.username:yk}")
	private String uavUsername;// 无人机系统登录用户名
	@Value("${uav.password:Yk@gxjk@123}")
	private String uavPassword;// 无人机系统登录用户名
	@Value("${uav.getdevicesurl:/thirdparty/api/v1/devices}")
	private String uavGetDevicesUrl;// 无人机系统登录URL
	@Value("${uav.token.checktime:3600}")
	private long uavTokenChecktime;
	private static long lastChecktime = 0;

	@Autowired
	private DeviceMapper deviceMapper;
	
	@Autowired
	private StringRedisTemplate stringRedisTemplate;

	@Override
	public ReturnT<String> execute(String param) throws Exception {
		String uav_access_token = getAccessToken();
		if (uav_access_token == null) {
			return FAIL;
		}

		DeviceReturnDTO deviceReturnDTO = getUavDomain3Data(uav_access_token);
		if (!isValid(deviceReturnDTO)) {
			return FAIL;
		}

		for (DeviceDTO deviceDTO : deviceReturnDTO.getData()) {
			String device_sn = deviceDTO.getDevice_sn();
			int rows = deviceMapper.countDeviceUavBySn(device_sn);
			if (rows == 0) {
				XxlJobLogger.log("机场不存在，新增{}", device_sn);
				// 入库机场数据
				deviceDTO.setDevice_id(UUID.randomUUID().toString());
				rows = deviceMapper.addDeviceUav(deviceDTO);
			} else {
				XxlJobLogger.log("机场已存在，更新{}", device_sn);
				rows = deviceMapper.updateDeviceUav(deviceDTO);
			}
			XxlJobLogger.log("机场 数据入库{}", rows > 0 ? "成功" : "失败");

			DeviceDTO children = deviceDTO.getChildren();
			if (children == null) {
				continue;
			}
			children.setPsn(device_sn);
			String device_sn2 = children.getDevice_sn();
			rows = deviceMapper.countDeviceUavBySn(device_sn2);
			if (rows == 0) {
				XxlJobLogger.log("飞行器不存在，新增{}", device_sn2);
				// 入库机场关联的飞行器设备
				children.setDevice_id(UUID.randomUUID().toString());
				rows = deviceMapper.addDeviceUav(children);
			} else {
				XxlJobLogger.log("飞行器已存在，更新{}", device_sn2);
				rows = deviceMapper.updateDeviceUav(children);
			}
			XxlJobLogger.log("飞行器 数据入库{}", rows > 0 ? "成功" : "失败");
		}

		return SUCCESS;
	}

	public static void main(String[] args) {
		String loginUrl = "http://***********:6789/system/api/v1/login";
		Map<String, String> body = new HashMap<>();
		body.put("username", "yk");
		body.put("password", "Yk@gxjk@123");
		String requestBody = GsonUtils.beanToJson(body);
		String response = HttpClientUtils.post(loginUrl, null, requestBody);
		LoginReturnDTO jsonToBean = GsonUtils.jsonToBean(response, LoginReturnDTO.class);
		System.out.println(jsonToBean);
		if (jsonToBean.getCode() != 0) {
			return;
		}
		body.remove("username");
		body.remove("password");
		body.put("X-Auth-Token", jsonToBean.getData().getAccess_token());

		String domain3DeviceUrl = "http://***********:6789" + "/thirdparty/api/v1/devices" + "?domain=3";
		response = HttpClientUtils.get(domain3DeviceUrl, body, 5000);
		DeviceReturnDTO deviceReturnDTO = GsonUtils.jsonToBean(response, DeviceReturnDTO.class);
		XxlJobLogger.log("deviceReturnDTO, {}", deviceReturnDTO);
	}

	private String getAccessToken() {
		long now = System.currentTimeMillis() / 1000;
		if (now - lastChecktime > uavTokenChecktime) {
			if (getUavAccessToken() != null) {
				stringRedisTemplate.opsForValue().set("its-job:uav_access_token", getUavAccessToken());
				lastChecktime = now;
			}
		}
		String uav_access_token = stringRedisTemplate.opsForValue().get("its-job:uav_access_token");

		if (uav_access_token == null) {
			uav_access_token = getUavAccessToken();
			if (uav_access_token != null) {
				stringRedisTemplate.opsForValue().set("its-job:uav_access_token", uav_access_token);
			}
		}
		return uav_access_token;
	}
	private String getUavAccessToken() {
		String url = uavHost + uavLoginUrl;
		Map<String, String> body = new HashMap<>();
		body.put("username", uavUsername);
		body.put("password", uavPassword);
		String requestBody = GsonUtils.beanToJson(body);
		String response = HttpClientUtils.post(url, null, requestBody);
		LoginReturnDTO jsonToBean = GsonUtils.jsonToBean(response, LoginReturnDTO.class);
		XxlJobLogger.log("DeviceUavSyncHandler, {}", jsonToBean.toString());
		if (jsonToBean.getCode() != 0) {
			XxlJobLogger.log("Login Fail, {}", jsonToBean.toString());
			return null;
		}
		try {
			return jsonToBean.getData().getAccess_token();
		} catch (Exception e) {
			XxlJobLogger.log("Get Uav access_token Fail, {}", e.getMessage());
		}
		return null;
	}

	private DeviceReturnDTO getUavDomain3Data(String uav_access_token) {
		Map<String, String> body = new HashMap<>();
		body.put("X-Auth-Token", uav_access_token);
		String domain3DeviceUrl = uavHost + uavGetDevicesUrl + "?domain=3";
		String response = HttpClientUtils.get(domain3DeviceUrl, body, 5000);
		XxlJobLogger.log("Get Domain3 Data, {}", response);

		DeviceReturnDTO deviceReturnDTO = GsonUtils.jsonToBean(response, DeviceReturnDTO.class);
		if (deviceReturnDTO == null) {
			uav_access_token = getUavAccessToken();
			stringRedisTemplate.opsForValue().set("its-job:uav_access_token", uav_access_token);
			response = HttpClientUtils.get(domain3DeviceUrl, body, 5000);
			XxlJobLogger.log("Reget Domain3 Data, {}", response);
			deviceReturnDTO = GsonUtils.jsonToBean(response, DeviceReturnDTO.class);
		}
		return deviceReturnDTO;
	}

	private boolean isValid(DeviceReturnDTO deviceReturnDTO) {
		if (deviceReturnDTO == null) {
			XxlJobLogger.log("Get Domain3 Fail , deviceReturnDTO is null");
			return false;
		}
		if (deviceReturnDTO.getCode() != 0) {
			XxlJobLogger.log("Get Domain3 Fail, {}", deviceReturnDTO.toString());
			return false;
		}
		List<DeviceDTO> data = deviceReturnDTO.getData();
		if (CollectionUtils.isEmpty(data)) {
			XxlJobLogger.log("No Demain3 Data 没有机场数据");
			return false;
		}
		return true;
	}
}
