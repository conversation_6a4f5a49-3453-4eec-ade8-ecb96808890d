<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itsjob.mapper.VideoMapper">

<resultMap type="com.bt.itsjob.domain.dto.VideoStatusDTO" id="VideoStatusMap">
	<id column="device_id" property="id"/>
	<result column="status" property="status"/>
</resultMap>

<select id="selectAllCamera" parameterType="java.util.Map" resultMap="VideoStatusMap">
SELECT device_id,`status` FROM device d WHERE d.device_type_no=4 AND source_id=#{sourceId}
</select>

<update id="batchUpdateStatus" parameterType="java.util.Map">
<foreach collection="dtos" item="d" open="" close="" separator=";">
UPDATE device SET `status`=#{d.status} WHERE device_id=#{d.id}
</foreach>
</update>

<resultMap type="com.bt.itsjob.domain.vo.other.FdOrgVideo" id="XfzVideoMap">
	<id column="org_id" property="fdOrgId"/>
	<result column="org_name" property="fdOrgName"/>
	<collection property="fdProvideList" ofType="com.bt.itsjob.domain.vo.other.FdProvide">
	  <id column="road_no" property="fdOrgProviderId"/>
	  <result column="road_name" property="fdOrgProviderName"/>
	  <collection property="fdConfigD" ofType="com.bt.itsjob.domain.vo.other.FdConfigD">
		  <id column="device_id" property="fdChannelId"/>
		  <result column="device_name" property="fdChannelName"/>
		  <result column="facility_no" property="fdConPlaceId"/>
		  <result column="facility_name" property="fdConPlaceName"/>
		  <result column="status" property="fdIsOnline"/>
		  <result column="use" property="fdEnabled"/>
		  <result column="location" property="fdLocationCoordinate"/>
		  <result column="mile_post" property="fdMilePost"/>
		</collection>
	</collection>
</resultMap>

<select id="selectAppCamera" resultMap="XfzVideoMap">
	SELECT 
		d.device_id,
		d.device_name,
		d.`status`,
		d.`use`,
		d.mile_post,
		CONCAT(d.lat,',',d.lng) AS location,
		f.facility_no,
		f.facility_name,
		r.road_no,
		r.road_name,
		o.xfz_id AS org_id,
		o.org_name 
	FROM device_camera dc,device d,facility f,road r,organization_road orgr,organization o
	WHERE dc.is_master_app=1 AND d.`use`=1 AND dc.device_id=d.device_id AND d.facility_no=f.facility_no AND f.road_no=r.road_no AND r.road_no=orgr.road_no AND orgr.org_id=o.org_id AND o.xfz_id IS NOT NULL
	AND d.device_type_no=4
	UNION ALL
	SELECT
		d.device_id,
		d.device_name,
		d.`status`,
		d.`use`,
		d.mile_post,
		CONCAT(d.lat,',',d.lng) AS location,
		f.facility_no,
		f.facility_name,
		r.road_no,
		r.road_name,
		o.xfz_id AS org_id,
		o.org_name
	FROM device_camera dc,device d,facility f,road r,organization_road orgr,organization o
	WHERE dc.entry_flag >0 AND d.`use`=1 AND dc.device_id=d.device_id AND d.facility_no=f.facility_no AND f.road_no=r.road_no AND r.road_no=orgr.road_no AND orgr.org_id=o.org_id AND o.xfz_id IS NOT NULL
	AND d.device_type_no=4 AND f.facility_type_no = 7 AND f.facility_no IN (SELECT facility_no FROM data_center_push_bayonet WHERE is_push = 1)
</select>

</mapper>