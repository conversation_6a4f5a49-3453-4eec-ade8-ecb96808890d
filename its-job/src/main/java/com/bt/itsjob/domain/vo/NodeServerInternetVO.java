package com.bt.itsjob.domain.vo;

public class NodeServerInternetVO {
	private Integer id;
	private String ipaddress;
	private Integer sourceId;
	private String checkTime;
	private Integer connStatus;
	private String sourceName;
	private String websocketType = "NodeServerInternet";

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getIpaddress() {
		return ipaddress;
	}

	public void setIpaddress(String ipaddress) {
		this.ipaddress = ipaddress;
	}

	public Integer getSourceId() {
		return sourceId;
	}

	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}

	public String getCheckTime() {
		return checkTime;
	}

	public void setCheckTime(String checkTime) {
		this.checkTime = checkTime;
	}

	public Integer getConnStatus() {
		return connStatus;
	}

	public void setConnStatus(Integer connStatus) {
		this.connStatus = connStatus;
	}

	public String getSourceName() {
		return sourceName;
	}

	public void setSourceName(String sourceName) {
		this.sourceName = sourceName;
	}

	public String getWebsocketType() {
		return websocketType;
	}

	public void setWebsocketType(String websocketType) {
		this.websocketType = websocketType;
	}

}
