package com.bt.itsjob.domain.dto;

/**
* <AUTHOR>
* @date 2021年3月16日 下午4:08:00
* @Description 组织机构DTO
 */
public class OrganizationDTO {
	private String orgId;// 组织机构id
	private String orgName;// 组织机构名称
	private Integer orgType;// 0部门;1公司
	private String orgTel;// 机构电话',
	private String leader;// 机构负责人
	private Integer use;// 0停用；1正常
	private Integer delStatus;//1-删除
	private Integer sort;// 排序号
	private String pid;// 父级orgId
	private Long createTime;// 记录生成时间
	private String xfzId;
	private String xfzPid;
	private String btId;
	
	public String getOrgId() {
		return orgId;
	}
	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public Integer getOrgType() {
		return orgType;
	}
	public void setOrgType(Integer orgType) {
		this.orgType = orgType;
	}
	public String getOrgTel() {
		return orgTel;
	}
	public void setOrgTel(String orgTel) {
		this.orgTel = orgTel;
	}
	public String getLeader() {
		return leader;
	}
	public void setLeader(String leader) {
		this.leader = leader;
	}
	public Integer getUse() {
		return use;
	}
	public void setUse(Integer use) {
		this.use = use;
	}
	public String getPid() {
		return pid;
	}
	public void setPid(String pid) {
		this.pid = pid;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public Long getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}
	public String getXfzId() {
		return xfzId;
	}
	public void setXfzId(String xfzId) {
		this.xfzId = xfzId;
	}
	public String getXfzPid() {
		return xfzPid;
	}
	public void setXfzPid(String xfzPid) {
		this.xfzPid = xfzPid;
	}
	public String getBtId() {
		return btId;
	}
	public void setBtId(String btId) {
		this.btId = btId;
	}
	public Integer getDelStatus() {
		return delStatus;
	}
	public void setDelStatus(Integer delStatus) {
		this.delStatus = delStatus;
	}
}
