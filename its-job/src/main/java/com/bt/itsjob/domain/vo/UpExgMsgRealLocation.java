package com.bt.itsjob.domain.vo;

public class UpExgMsgRealLocation {

	private String dateTime;//20210806175038
	private String lon;//113.132696
	private String lat;//22.5983
//	private Integer vec1;//卫星定位车载终端设备上传的行车速度
//	private Integer vec2;//车辆行驶记录设备上传的行车速度
//	private Integer vec3;//车辆行驶记录设备上传的行车速度
	private Alarm alarm;
	private int direction;//0-359，正北为0，顺时针
	public Alarm getAlarm() {
		return alarm;
	}
	public void setAlarm(Alarm alarm) {
		this.alarm = alarm;
	}
	public int getDirection() {
		return direction;
	}
	public void setDirection(int direction) {
		this.direction = direction;
	}
	public String getDateTime() {
		return dateTime;
	}
	public void setDateTime(String dateTime) {
		this.dateTime = dateTime;
	}
	public String getLon() {
		return lon;
	}
	public void setLon(String lon) {
		this.lon = lon;
	}
	public String getLat() {
		return lat;
	}
	public void setLat(String lat) {
		this.lat = lat;
	}
//	public Integer getVec1() {
//		return vec1;
//	}
//	public void setVec1(Integer vec1) {
//		this.vec1 = vec1;
//	}
//	public Integer getVec2() {
//		return vec2;
//	}
//	public void setVec2(Integer vec2) {
//		this.vec2 = vec2;
//	}
//	public Integer getVec3() {
//		return vec3;
//	}
//	public void setVec3(Integer vec3) {
//		this.vec3 = vec3;
//	}
	static class Alarm {
		private int gnssAntennaDisconnect;//0,
		private int laneDepartureError;//0,
		private int terminalLcdError;//0,
		private int earlyWarning;//0,
		private int stopTimeout;//0,
		private int vssError;//0,
		private int inOutRoute;//0,
		private int terminalMainPowerUnderVoltage;//0,
		private int ttsModuleError;//0,
		private int oilError;//0,
		private int fatigueDriving;//0,
		private int overSpeed;//0,
		private int gnssAntennaShortCircuit;//0,
		private int cameraError;//0,
		private int illegalIgnition;//0,
		private int emergencyAlarm;//0,
		private int roadDrivingTimeout;//0,
		private int stolen;//0,
		private int cumulativeDrivingTimeout;//1,
		private int gnssModuleError;//0,
		private int terminalMainPowerFailure;//0,
		private int illegalMove;//0,
		private int inOutArea;//0
		public int getGnssAntennaDisconnect() {
			return gnssAntennaDisconnect;
		}
		public void setGnssAntennaDisconnect(int gnssAntennaDisconnect) {
			this.gnssAntennaDisconnect = gnssAntennaDisconnect;
		}
		public int getLaneDepartureError() {
			return laneDepartureError;
		}
		public void setLaneDepartureError(int laneDepartureError) {
			this.laneDepartureError = laneDepartureError;
		}
		public int getTerminalLcdError() {
			return terminalLcdError;
		}
		public void setTerminalLcdError(int terminalLcdError) {
			this.terminalLcdError = terminalLcdError;
		}
		public int getEarlyWarning() {
			return earlyWarning;
		}
		public void setEarlyWarning(int earlyWarning) {
			this.earlyWarning = earlyWarning;
		}
		public int getStopTimeout() {
			return stopTimeout;
		}
		public void setStopTimeout(int stopTimeout) {
			this.stopTimeout = stopTimeout;
		}
		public int getVssError() {
			return vssError;
		}
		public void setVssError(int vssError) {
			this.vssError = vssError;
		}
		public int getInOutRoute() {
			return inOutRoute;
		}
		public void setInOutRoute(int inOutRoute) {
			this.inOutRoute = inOutRoute;
		}
		public int getTerminalMainPowerUnderVoltage() {
			return terminalMainPowerUnderVoltage;
		}
		public void setTerminalMainPowerUnderVoltage(int terminalMainPowerUnderVoltage) {
			this.terminalMainPowerUnderVoltage = terminalMainPowerUnderVoltage;
		}
		public int getTtsModuleError() {
			return ttsModuleError;
		}
		public void setTtsModuleError(int ttsModuleError) {
			this.ttsModuleError = ttsModuleError;
		}
		public int getOilError() {
			return oilError;
		}
		public void setOilError(int oilError) {
			this.oilError = oilError;
		}
		public int getFatigueDriving() {
			return fatigueDriving;
		}
		public void setFatigueDriving(int fatigueDriving) {
			this.fatigueDriving = fatigueDriving;
		}
		public int getGnssAntennaShortCircuit() {
			return gnssAntennaShortCircuit;
		}
		public void setGnssAntennaShortCircuit(int gnssAntennaShortCircuit) {
			this.gnssAntennaShortCircuit = gnssAntennaShortCircuit;
		}
		public int getCameraError() {
			return cameraError;
		}
		public void setCameraError(int cameraError) {
			this.cameraError = cameraError;
		}
		public int getIllegalIgnition() {
			return illegalIgnition;
		}
		public void setIllegalIgnition(int illegalIgnition) {
			this.illegalIgnition = illegalIgnition;
		}
		public int getEmergencyAlarm() {
			return emergencyAlarm;
		}
		public void setEmergencyAlarm(int emergencyAlarm) {
			this.emergencyAlarm = emergencyAlarm;
		}
		public int getRoadDrivingTimeout() {
			return roadDrivingTimeout;
		}
		public void setRoadDrivingTimeout(int roadDrivingTimeout) {
			this.roadDrivingTimeout = roadDrivingTimeout;
		}
		public int getStolen() {
			return stolen;
		}
		public void setStolen(int stolen) {
			this.stolen = stolen;
		}
		public int getCumulativeDrivingTimeout() {
			return cumulativeDrivingTimeout;
		}
		public void setCumulativeDrivingTimeout(int cumulativeDrivingTimeout) {
			this.cumulativeDrivingTimeout = cumulativeDrivingTimeout;
		}
		public int getGnssModuleError() {
			return gnssModuleError;
		}
		public void setGnssModuleError(int gnssModuleError) {
			this.gnssModuleError = gnssModuleError;
		}
		public int getTerminalMainPowerFailure() {
			return terminalMainPowerFailure;
		}
		public void setTerminalMainPowerFailure(int terminalMainPowerFailure) {
			this.terminalMainPowerFailure = terminalMainPowerFailure;
		}
		public int getIllegalMove() {
			return illegalMove;
		}
		public void setIllegalMove(int illegalMove) {
			this.illegalMove = illegalMove;
		}
		public int getInOutArea() {
			return inOutArea;
		}
		public void setInOutArea(int inOutArea) {
			this.inOutArea = inOutArea;
		}
		public int getOverSpeed() {
			return overSpeed;
		}
		public void setOverSpeed(int overSpeed) {
			this.overSpeed = overSpeed;
		}
	}

}
