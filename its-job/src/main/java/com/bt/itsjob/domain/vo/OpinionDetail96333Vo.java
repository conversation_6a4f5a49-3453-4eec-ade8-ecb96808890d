package com.bt.itsjob.domain.vo;

public class OpinionDetail96333Vo {
	private String acceptDeptCode;//: "E09.645.GGJ.LWSFZX.GSYZ.LZGS"
	private String acceptDeptName;//: "柳州高速分中心"
	private String busiCode;//: "TS20210106143233"
	private String busiFieldCode;//: "294"
	private String busiFieldName;//: "高速公路->路网服务->车辆限（禁）行"
	private Integer busiSource;//: 1
	private String opinionContent;//: "(12345工单DH202101051190）群众来电表示近期发现广西区内的泉南高速，柳州市鹿寨东互通收费站至柳州东收费站路段全程限速100km/h，而柳州东至南宁东路段全程限速120km/h，其表示为何这两个路段道路宽度均是一致，但柳州市鹿寨东互通收费站至柳州东收费站且限速100km/h，对此很不解，现希望相关部门能告知为何柳州市鹿寨东互通收费站至柳州东收费站且限速100km/h。群众表示仅想知道该路段限速100km/h的原因，并表示2021年1月8日至1月15日期间均是无法联系，如部门需联系其告知结果请在2021年1月15日之后，或发送短信。请承办单位及时签收并与群众联系，办理时限规定于2021年1月17日18：00前反馈处理结果。"
	private String eventResult;
	private String callerName;//: "黄先生"
	private String callerNum;//: "18077235188"
	private Long connectTime;//: 1609914619000
	private Long createTime;//: 1609914780000
	private Integer createUserId;//: 669
	private String createUserName;//: "李玉颜"
	private Long deadline;//: 1612506753000
	private Integer handleMode;//: 1
	private Integer id;//: 435
	private String processId;//": "270439",
	private Long updateTime;//: 1609914793000
	private Integer	updateUserId;//: 669
	private String updateUserName;//: "李玉颜"
	private String object;//对象
	private String status;
	
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getProcessId() {
		return processId;
	}

	public void setProcessId(String processId) {
		this.processId = processId;
	}

	public String getAcceptDeptCode() {
		return acceptDeptCode;
	}

	public void setAcceptDeptCode(String acceptDeptCode) {
		this.acceptDeptCode = acceptDeptCode;
	}

	public String getAcceptDeptName() {
		return acceptDeptName;
	}

	public void setAcceptDeptName(String acceptDeptName) {
		this.acceptDeptName = acceptDeptName;
	}

	public String getBusiCode() {
		return busiCode;
	}

	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}

	public String getBusiFieldCode() {
		return busiFieldCode;
	}

	public void setBusiFieldCode(String busiFieldCode) {
		this.busiFieldCode = busiFieldCode;
	}

	public String getBusiFieldName() {
		return busiFieldName;
	}

	public void setBusiFieldName(String busiFieldName) {
		this.busiFieldName = busiFieldName;
	}

	public Integer getBusiSource() {
		return busiSource;
	}

	public void setBusiSource(Integer busiSource) {
		this.busiSource = busiSource;
	}

	public String getOpinionContent() {
		return opinionContent;
	}

	public void setOpinionContent(String opinionContent) {
		this.opinionContent = opinionContent;
	}

	public String getEventResult() {
		return eventResult;
	}

	public void setEventResult(String eventResult) {
		this.eventResult = eventResult;
	}

	public String getCallerName() {
		return callerName;
	}

	public void setCallerName(String callerName) {
		this.callerName = callerName;
	}

	public String getCallerNum() {
		return callerNum;
	}

	public void setCallerNum(String callerNum) {
		this.callerNum = callerNum;
	}

	public Long getConnectTime() {
		return connectTime;
	}

	public void setConnectTime(Long connectTime) {
		this.connectTime = connectTime;
	}

	public Long getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}

	public Integer getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}

	public Long getDeadline() {
		return deadline;
	}

	public void setDeadline(Long deadline) {
		this.deadline = deadline;
	}

	public Integer getHandleMode() {
		return handleMode;
	}

	public void setHandleMode(Integer handleMode) {
		this.handleMode = handleMode;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Long getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Long updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}

	public String getUpdateUserName() {
		return updateUserName;
	}

	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	
	public String getObject() {
		return object;
	}

	public void setObject(String object) {
		this.object = object;
	}

	public class ResultVo {
		private OpinionDetail96333Vo result;

		public OpinionDetail96333Vo getResult() {
			return result;
		}

		public void setResult(OpinionDetail96333Vo result) {
			this.result = result;
		}

	}
}
