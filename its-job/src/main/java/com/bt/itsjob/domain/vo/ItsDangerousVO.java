package com.bt.itsjob.domain.vo;

public class ItsDangerousVO implements Comparable<ItsDangerousVO> {
	private String vehicleid;
	private String uptime;
	private String time;
	private String speed;
	private String milePost;
	private DangerousContentVO content;
	// 计算出来的
	private Integer roadNo;
	public String getVehicleid() {
		return vehicleid;
	}
	public void setVehicleid(String vehicleid) {
		this.vehicleid = vehicleid;
	}
	public String getUptime() {
		return uptime;
	}
	public void setUptime(String uptime) {
		this.uptime = uptime;
	}
	public String getTime() {
		return time;
	}
	public void setTime(String time) {
		this.time = time;
	}
	public String getSpeed() {
		return speed;
	}
	public void setSpeed(String speed) {
		this.speed = speed;
	}
	public String getMilePost() {
		return milePost;
	}
	public void setMilePost(String milePost) {
		this.milePost = milePost;
	}
	public DangerousContentVO getContent() {
		return content;
	}
	public void setContent(DangerousContentVO content) {
		this.content = content;
	}
	public Integer getRoadNo() {
		return roadNo;
	}
	public void setRoadNo(Integer roadNo) {
		this.roadNo = roadNo;
	}
	@Override
	public int compareTo(ItsDangerousVO o) {
		return o.getTime().compareTo(this.getTime());
	}
}
