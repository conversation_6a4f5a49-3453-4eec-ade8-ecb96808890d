package com.bt.itsjob.domain.vo;

import java.io.Serializable;

public class AccidentAnalysisVO implements Serializable {
    private String name;
    private String id;// 分公司id
    private Integer accidentNum;// 事故数量
    private Integer injureNum;// 受伤人数
    private Integer deathNum;// 死亡人数
    private String accidentHuanbi;// 事故数环比
    private String dealTimeHuanbi;// 处置时长环比
    private String avgDealTime;// 处置时长
    private long avgDealTimeSec;// 处置时长(秒)
    private String content;// 发送短信的拼接内容

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getAccidentNum() {
        return accidentNum;
    }

    public void setAccidentNum(Integer accidentNum) {
        this.accidentNum = accidentNum;
    }

    public Integer getInjureNum() {
        return injureNum;
    }

    public void setInjureNum(Integer injureNum) {
        this.injureNum = injureNum;
    }

    public Integer getDeathNum() {
        return deathNum;
    }

    public void setDeathNum(Integer deathNum) {
        this.deathNum = deathNum;
    }

	public String getAccidentHuanbi() {
		return accidentHuanbi;
	}

	public void setAccidentHuanbi(String accidentHuanbi) {
		this.accidentHuanbi = accidentHuanbi;
	}

	public String getDealTimeHuanbi() {
		return dealTimeHuanbi;
	}

	public void setDealTimeHuanbi(String dealTimeHuanbi) {
		this.dealTimeHuanbi = dealTimeHuanbi;
	}

	public String getAvgDealTime() {
		return avgDealTime;
	}

	public void setAvgDealTime(String avgDealTime) {
		this.avgDealTime = avgDealTime;
	}

	public long getAvgDealTimeSec() {
		return avgDealTimeSec;
	}

	public void setAvgDealTimeSec(long avgDealTimeSec) {
		this.avgDealTimeSec = avgDealTimeSec;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}
}