spring:
  application:
    name: its-job
  profiles:
    active: dev
  main:
    # 允许重写bean
    allow-bean-definition-overriding: true
  shardingsphere:
    datasource:
      # 数据源名称，多个使用(,)分割
      names: m1
      # 数据源的详细配置，多个需要分别配置
      m1:
        type: com.mysql.cj.jdbc.MysqlXADataSource
        driver-class-name: com.mysql.jdbc.Driver
        url: *******************************************************************************************************************************************
        username: root
        user: root
        password: 8P4Jkrvm@jAkN7u
    sharding:
      tables:
                 # 数据表的逻辑表名，即一个数据表的名字，可以是不存在的
        alarmshard:
          actual-data-nodes: m1.alarm_${2022..2025}
          key-generator:
            column: id
            type: SNOWFLAKE
          table-strategy:
            standard:
              sharding-column: create_time
              precise-algorithm-class-name: com.bt.itsjob.sharding.AlarmPreciseSharding
              range-algorithm-class-name: com.bt.itsjob.sharding.AlarmRangeSharding
    props:
      sql:
        show: true
#MQTT客户端
mqtt:
    host: tcp://**************:5220
    clientId: mqtt_publish
    options:
#        userName: kadiya
#        password: r18?:1APw5U-
        userName: yunkong
        password: Xfzzhyk@20240828#
#        这里表示会话不过期
        cleanSession: false
#     配置一个默认的主题，加载时不会用到，只能在需要时手动提取
#        defaultTopic: devops
#        KeepAliveInterval: 10
        #断线重连方式，自动重新连接与会话不过期配合使用会导致
        #断线重新连接后会接收到断线期间的消息。需要更改设置请看password联系我
        automaticReconnect: true
        connectionTimeout: 30
        # 最大链接数
        maxInflight: 100

--- 
#开发环境 
spring: 
  profiles: dev 
  cloud: 
    nacos: 
      config: 
        username: nacos 
        password: UM1h5uZ7WHDypJK 
        server-addr: ***********:8848 
        file-extension: yaml 
        namespace: ns_dev 
        extension-configs[0].data-id: bt-itsway-common.yaml 
        extension-configs[1].data-id: sms.yaml 
      discovery: 
        username: nacos 
        password: UM1h5uZ7WHDypJK 
        server-addr: ***********:8848 
        namespace: ns_dev 
         
--- 
#测试环境 
spring: 
  profiles: test 
  cloud: 
    nacos: 
      config: 
        username: nacos 
        password: ds0f@9jiF$k83Tc 
        server-addr: 172.16.8.32:8848 
        file-extension: yaml 
        namespace: ns_prod 
        extension-configs[0].data-id: bt-itsway-common.yaml 
        extension-configs[1].data-id: sms.yaml 
      discovery: 
        username: nacos 
        password: ds0f@9jiF$k83Tc 
        server-addr: 172.16.8.32:8848 
        namespace: ns_prodv 