package com.bt.itspbx.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nls.client.AccessToken;
import com.bt.itscore.config.ApplicationContextUtils;
import com.bt.itscore.config.OssConfig;
import com.bt.itscore.exception.FailException;
import com.bt.itscore.utils.GsonUtils;
import com.bt.itspbx.constants.ALiYunSpeechConstant;
import com.bt.itspbx.domain.entity.SpeechAudioBean;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * 文字转语音工具类
 *
 * <AUTHOR>
 * @since 2024-1-17
 */
public class TextToSpeechUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(TextToSpeechUtils.class);
    private static final long MIN_TIME = 30; // token失效时间-当前时间的最小预留值
    private static final Object LOCK = new Object();
    private static AccessToken accessToken; // 阿里云token（认证+权限）
    private static String domainUrl = "";


    public static JSONObject sendRequestToSpeechServer(SpeechAudioBean speechAudioBean) {
        if (accessToken == null) {
            throw new FailException("获取文字转语音的token失败，token为空");
        }
        // 组装请求参数
        String bodyContent = assembleRequestBodyParams(speechAudioBean);
        // 发起请求
        RequestBody reqBody = RequestBody.create(MediaType.parse("application/json"), bodyContent);
        Request request = new Request.Builder().url(ALiYunSpeechConstant.SPEECH_ASYNC_URL).header("Content-Type", "application/json").post(reqBody).build();
        Response response = null;
        try {
            OkHttpClient client = new OkHttpClient();
            response = client.newCall(request).execute();
            String contentType = response.header("Content-Type");
            // 获取结果，并根据返回进一步进行处理。
            if (response.body() == null) {
                LOGGER.error("文字转语音返回的参数异常：{}", GsonUtils.beanToJson(response));
                return null;
            }
            String result = response.body().string();
            JSONObject resultJson = JSON.parseObject(result);
            return resultJson;
        } catch (IOException ex) {
            LOGGER.error("文字转语音请求失败：" + ex.getCause());
            throw new FailException("文字转语音请求失败");
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    private static String assembleRequestBodyParams(SpeechAudioBean speechAudioBean) {
        String token = accessToken.getToken();
        // 拼接HTTP Post请求的消息体内容。
        JSONObject context = new JSONObject();
        // device_id设置，可以设置为自定义字符串或者设备信息id（暂时未使用）
        context.put("device_id", "my_device_id");
        JSONObject header = new JSONObject();
        // 设置你的appkey。获取Appkey请前往控制台：https://nls-portal.console.aliyun.com/applist
        header.put("appkey", ALiYunSpeechConstant.APP_KEY);
        // 设置你的Token。获取Token具体操作，请参见：https://help.aliyun.com/document_detail/450514.html
        header.put("token", token);

        JSONObject payload = new JSONObject();
        // 可选，是否设置回调。如果设置，则服务端在完成长文本语音合成之后回调用户此处设置的回调接口，将请求状态推送给用户侧。
        payload.put("enable_notify", true);
        payload.put("notify_url", domainUrl + ALiYunSpeechConstant.SPEECH_CALLBACK_URL); //回调方法用于推送消息（单个客户即可，即创建者）
        payload.put("tts_request", speechAudioBean);

        JSONObject json = new JSONObject();
        json.put("context", context);
        json.put("header", header);
        json.put("payload", payload);
        String bodyContent = json.toJSONString();
        LOGGER.info("POST Body Content: " + bodyContent);
        return bodyContent;
    }

    /// 根据特定信息轮询检查某个请求在服务端的合成状态，轮询操作非必须，如果设置了回调url，则服务端会在合成完成后主动回调。
    public static void completeCallback(String url, String appKey, String token, String task_id, String request_id) {
        String fullUrl = url + "?appkey=" + appKey + "&task_id=" + task_id + "&token=" + token + "&request_id=" + request_id;
        while (true) {
            Request request = new Request.Builder().url(fullUrl).get().build();
            try {
                OkHttpClient client = new OkHttpClient();
                Response response = client.newCall(request).execute();
                String result = response.body().string();
                response.close();
                JSONObject resultJson = JSON.parseObject(result);
                if (resultJson.containsKey("error_code") && resultJson.getIntValue("error_code") == 20000000 && resultJson.containsKey("data") && resultJson.getJSONObject("data").getString("audio_address") != null) {
                    LOGGER.info("Tts Finished! task_id = " + resultJson.getJSONObject("data").getString("task_id"));
                    LOGGER.info("Tts Finished! audio_address = " + resultJson.getJSONObject("data").getString("audio_address"));
                    break;
                } else {
                    LOGGER.info("Tts Queuing...");
                }
                // 每隔10秒钟轮询一次状态。
                Thread.sleep(10000);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static JSONObject textToSpeech(String text) {
        if (StringUtils.isBlank(text)) {
            throw new FailException("输入文字长度为空，请检查");
        }
        // 初始化token
        initAccessToken();
        LOGGER.info("get token: " + accessToken.getToken() + ", expire time: " + accessToken.getExpireTime());
        // 设置音频相关的参数
        SpeechAudioBean audioBean = new SpeechAudioBean();
        audioBean.setText(text);
        audioBean.setVoice(ALiYunSpeechConstant.SPEECH_VOICE);
        audioBean.setFormat(ALiYunSpeechConstant.SPEECH_FORMAT);
        audioBean.setSample_rate(ALiYunSpeechConstant.SPEECH_SAMPLE_RATE);
        audioBean.setVolume(ALiYunSpeechConstant.SPEECH_VOLUME);
        audioBean.setSpeech_rate(ALiYunSpeechConstant.SPEECH_RATE);
        audioBean.setEnable_subtitle(false);
        JSONObject resultJson = sendRequestToSpeechServer(audioBean);
        return resultJson;
    }

 /*   public static void main(String[] args) {
        String text = "请事故车车主在事故车后方，150米外摆放安全警示标志，人员尽快撤离到   安全区域，请勿随意走动，注意自身安全，如有需要请拨打96333 高速公路服务电话。";
        textToSpeech(text);
    }*/

    private static void initAccessToken() {
        long currentTime = System.currentTimeMillis() / 1000;
        if (accessToken != null && accessToken.getExpireTime() - currentTime > MIN_TIME) {
            return;
        }
        synchronized (LOCK) {
            OssConfig ossConfig = (OssConfig) ApplicationContextUtils.getBean(OssConfig.class);
            if (ossConfig == null || StringUtils.isEmpty(ossConfig.getAccessId()) || StringUtils.isEmpty(ossConfig.getAccessSecret())) {
                LOGGER.error("Get the aliyun text to speech access token is error, oss config is null, error msg: {}", GsonUtils.beanToJson(ossConfig));
                accessToken = null;
                return;
            }
            String accessKeyId = ossConfig.getAccessId();
            String accessKeySecret = ossConfig.getAccessSecret();
            domainUrl = ossConfig.getCallbackUrl();
            AccessToken token = new AccessToken(accessKeyId, accessKeySecret);
            try {
                token.apply();
                String tokenStr = token.getToken();
                long expireTime = token.getExpireTime();
                if (StringUtils.isEmpty(tokenStr) || expireTime - currentTime < MIN_TIME) {
                    accessToken = null;
                    LOGGER.error("The aliyun text to speech access token is null");
                    return;
                }
                accessToken = token;
            } catch (IOException e) {
                LOGGER.error("Get the aliyun text to speech access token is error: " + e.getCause());
                accessToken = null;
            }
        }
    }
}