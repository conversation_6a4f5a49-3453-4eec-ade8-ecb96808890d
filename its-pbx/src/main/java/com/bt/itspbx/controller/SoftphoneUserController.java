package com.bt.itspbx.controller;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bt.itscore.auth.Login;
import com.bt.itscore.domain.dto.PageDTO;
import com.bt.itscore.domain.vo.PageVO;
import com.bt.itscore.domain.vo.ResponseVO;
import com.bt.itspbx.domain.dto.SoftphoneUserDTO;
import com.bt.itspbx.service.SoftphoneUserService;

/**
 * 
 * @Description: 软件电话用户权限分配管理
 * <AUTHOR>
 * @date 2023年11月17日 下午2:37:37
 *
 */
@RestController
@RequestMapping("softphoneUser")
public class SoftphoneUserController {
	@Autowired
	private SoftphoneUserService softphoneUserService; // 软件电话

	/**
	 * @api {POST} /softphoneUser/pageSoftphoneUser 软件电话配置用户权限分页查询 /softphoneUser/pageSoftphoneUser
	 * @apiDescription 软件电话配置用户权限分页查询；创建人：宁艺强，修改人：无
	 * @apiGroup SoftphoneUserController-软件电话用户权限分配管理
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam {number} page 页码，必须大于0
	 * @apiParam {number} limit 每页的数目，必须大于0
	 * @apiParam (Success 200) {String} deviceId 软件电话ID
	 * @apiParam (Success 200) {String} deviceCode 软电电话名称
	 * @apiParam (Success 200) {String} deviceName 软电电话名称
	 * @apiParam (Success 200) {String} userId 用户ID
	 * @apiParam (Success 200) {String} userName 用户名称
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"deviceId": "75e4563d-9ccf-4bcf-baee-aee5a5478e24",	
	 * 			"deviceCode": "1000002",
	 * 			"deviceName": "交科ITS软电话03",
	 * 			"userId": "d09ef18d-9bd3-4d8e-bc33-3bdcdb47cfdb",
	 *  		"userName": "马伟智"
	 *   	}
	 * @apiSuccess (Success 200) {String} deviceId 软件电话ID
	 * @apiSuccess (Success 200) {String} deviceCode 软电电话名称
	 * @apiSuccess (Success 200) {String} deviceName 软电电话名称
	 * @apiSuccess (Success 200) {String} userId 用户ID
	 * @apiSuccess (Success 200) {String} userName 用户名称
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * {	
	 *   "total": 9,
	 * 	 "items": [
	 *		{ 
	 * 			"deviceId": "75e4563d-9ccf-4bcf-baee-aee5a5478e24",	
	 * 			"deviceCode": "1000002",
	 * 			"deviceName": "交科ITS软电话03",
	 * 			"userId": "d09ef18d-9bd3-4d8e-bc33-3bdcdb47cfdb",
	 *  		"userName": "马伟智"
	 *   	}
	 *	]
	 *}
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /softphoneUser/pageSoftphoneUser
	 */
	@Login
	@PostMapping("pageSoftphoneUser")
	public Object pageSoftphoneUser(@Valid PageDTO pageDTO, @RequestBody SoftphoneUserDTO dto,
			HttpServletRequest request) {
		return new PageVO(softphoneUserService.page(dto, pageDTO));
	}

	/**
	 * @api {POST} /softphoneUser/addSoftphoneUser 分配软件电话用户权限 /softphoneUser/addSoftphoneUser
	 * @apiDescription 分配软件电话用户权限；创建人：宁艺强，修改人：无
	 * @apiGroup SoftphoneUserController-软件电话用户权限分配管理
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String} deviceId 软件电话ID
	 * @apiParam (Success 200) {String} userId 配置权限的用户id
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"deviceId": "75e4563d-9ccf-4bcf-baee-aee5a5478e24",	
	 * 			"userId": "d09ef18d-9bd3-4d8e-bc33-3bdcdb47cfdb"
	 *   	}
	 * @apiSuccess (Success 200) {Integer} code 编码（ 0-失败、1-成功）
	 * @apiSuccess (Success 200) {String} message 反馈消息
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 *		{ 
	 * 			"code": 1,
	 * 			"message": "SUCCESS",
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /softphoneUser/addSoftphoneUser
	 */
	@Login
	@PostMapping("addSoftphoneUser")
	public ResponseVO addSoftphoneUser(@Valid @RequestBody SoftphoneUserDTO dto, BindingResult result,
			HttpServletRequest request) {
		if (StringUtils.isBlank(dto.getDeviceId())) {
			return new ResponseVO("配置用户权限软件电话不能为空", 400);
		}
		return softphoneUserService.save(dto);
	}

	/**
	 * @api {POST} /softphoneUser/checkCount 验证软件电话是否配置用户 /softphoneUser/checkCount
	 * @apiDescription 验证软件电话是否配置用户；创建人：宁艺强，修改人：无
	 * @apiGroup SoftphoneUserController-软件电话用户权限分配管理
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String} deviceId 软件电话ID
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"deviceId": "75e4563d-9ccf-4bcf-baee-aee5a5478e24"
	 *   	}
	 * @apiSuccess (Success 200) {Integer} code 编码（ 0-未配置、1-已配置）
	 * @apiSuccess (Success 200) {String} message 反馈消息
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * 	[
	 *		{ 
	 * 			"code": 1,
	 * 			"message": "SUCCESS",
	 *   	}
	 *	]
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /softphoneUser/checkCount
	 */
	@PostMapping("checkCount")
	public ResponseVO checkCount(@Valid @RequestBody SoftphoneUserDTO dto, BindingResult result,
			HttpServletRequest request) {
		String deviceId = dto.getDeviceId();
		if (StringUtils.isBlank(deviceId)) {
			return new ResponseVO("配置用户权限软件电话不能为空", 400);
		}
		boolean ret = softphoneUserService.checkCount(deviceId);
		return new ResponseVO(ret);
	}

	/**
	 * @api {POST} /softphoneUser/selectUser 根据软件电话ID查询分配权限的用户 /softphoneUser/selectUser
	 * @apiDescription 根据软件电话ID查询分配权限的用户；创建人：宁艺强，修改人：无
	 * @apiGroup SoftphoneUserController-软件电话用户权限分配管理
	 * @apiHeader {String} Authorization 用户登录后的token.
	 * @apiParam (Success 200) {String} deviceId 软件电话ID
	 * @apiParamExample {json} Request-Example: 
	 *		{ 
	 * 			"deviceId": "75e4563d-9ccf-4bcf-baee-aee5a5478e24"
	 *   	}
	 * @apiSuccess (Success 200) {String} orgId 单位id
	 * @apiSuccess (Success 200) {String} orgName 用户单位名称
	 * @apiSuccess (Success 200) {String} userId 用户ID
	 * @apiSuccess (Success 200) {String} userName 用户名称
	 * @apiSuccessExample {json} Success-Response: HTTP/1.1 200 OK 
	 * {	
	 *   "total": 9,
	 * 	 "items": [
	 *		{ 
	 * 			"orgId": "3b1eee8c-46eb-4219-8d55-4005f13c749a",	
	 * 			"orgName": "智慧高速指挥中心",
	 * 			"userId": "d09ef18d-9bd3-4d8e-bc33-3bdcdb47cfdb",
	 *  		"userName": "马伟智"
	 *   	}
	 *	]
	 *}
	 * @apiErrorExample {json} Success-Response: HTTP/1.1 200 OK code=401用户未登录状态 
	 * 		{ 
	 * 			"code: 401 
	 * 		}
	 * @apiSampleRequest /softphoneUser/selectUser
	 */
	@Login
	@PostMapping("selectUser")
	public Object selectUser(@RequestBody SoftphoneUserDTO dto, BindingResult result, HttpServletRequest request) {
		String deviceId = dto.getDeviceId();
		if (StringUtils.isBlank(deviceId)) {
			return new ResponseVO("查询配置用户权限软件电话不能为空", 400);
		}
		return softphoneUserService.selectUser(deviceId);
	}

}
