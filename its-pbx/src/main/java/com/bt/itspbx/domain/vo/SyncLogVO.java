package com.bt.itspbx.domain.vo;

/**
 * 
 * @Description: 同步记录
 * <AUTHOR>
 * @date 2023年11月20日 上午9:07:55
 *
 */
public class SyncLogVO {
	private String id;// 设备编号
	private Integer type;//同步类型： 10-自动同步设备信息,11-手动同步设备信息;20-自动同步设备状态,21-手动设备状态;
	private String sipCode;// 所属SIP服务编号信息
	private String description;// 同步内容描述
	private Integer syncFlag;// 同步标志 1-成功 0-失败
	private Long createTime;// 创建时间
	private String creater;// 同步人员
	private Long updateTime;// 更新时间
	private Integer readFlag;//已读、确认标识 1-确认 0-未确认
	private String remark;//失败原因备注
	private Integer moreFlag;// 是否再次同步 1-是 0-否
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public String getSipCode() {
		return sipCode;
	}
	public void setSipCode(String sipCode) {
		this.sipCode = sipCode;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	
	public Integer getSyncFlag() {
		return syncFlag;
	}
	public void setSyncFlag(Integer syncFlag) {
		this.syncFlag = syncFlag;
	}
	public Long getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Long createTime) {
		this.createTime = createTime;
	}
	public String getCreater() {
		return creater;
	}
	public void setCreater(String creater) {
		this.creater = creater;
	}
	public Long getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Long updateTime) {
		this.updateTime = updateTime;
	}
	public Integer getReadFlag() {
		return readFlag;
	}
	public void setReadFlag(Integer readFlag) {
		this.readFlag = readFlag;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public Integer getMoreFlag() {
		return moreFlag;
	}
	public void setMoreFlag(Integer moreFlag) {
		this.moreFlag = moreFlag;
	}
	

}
