package com.bt.itspbx.domain.dto;
/**
 * 
 * @Description: 用于存储接收信息分析
 * <AUTHOR>
 * @date 2024年1月2日 上午9:06:27
 *
 */
public class ReceiveDataDTO {
	private String id; //id
	private String type; // 存储消息类别：1-设备状态 2-通话状态 3-节点链路监听
	private String message; // 接收信息内容
	private String time; // 接收时间
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getTime() {
		return time;
	}
	public void setTime(String time) {
		this.time = time;
	}
	
}
