package com.bt.itspbx.domain.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class SipDTO {
	private String id; // id
	@NotBlank(message = "SIP服务编号不能为空")
	private String sipCode;// 所属SIP服务编号
	@NotBlank(message = "SIP服务器名称不能为空")
	private String sipName;// SIP服务器名称
	@NotNull(message = "设备类型不能为空")
	private Integer type;// 节点类型 0-路段节点 1-上云节点
	@NotBlank(message = "SIP节点唯一标识不能为空")
	private String entityId;// SIP节点唯一标识，通常是绑定IP的MAC地址
	@NotBlank(message = "SIP节点主机名称不能为空")
	private String hostName;// SIP节点主机名称
	private String publicAddress;// SIP节点提供SIP服务的公网访问地址
	private String privateAddress;// SIP节点提供SIP服务的内网访问地址
	private String orgId;// 所属机构
	private String asteriskVersion;// asterisk版本信息
	private String arisVersion;// ARIS版本信息
	private Integer hbInterval;// 心跳时间
	private Integer hbTimeout;// 心跳间隔
	private Integer status;// 在线状态 1在线 0离线
	private Long onlineTime;// 在线时间
	private Long keepTime;// 保持心跳时间
	private Integer waitTime; // 在线判断等待时间（秒）
	private Integer use;// 使用标志 1-使用 0-停用
	private String remark;// 备注说明
	private String domainName;// 内网域名
	private String port;// 端口号
	private List<String> ids;// 批量删除使用
	private Integer syncType;// 手动同步类型 1-单节同步，2-全部节点同步

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getSipCode() {
		return sipCode;
	}

	public void setSipCode(String sipCode) {
		this.sipCode = sipCode;
	}

	public Integer getWaitTime() {
		return waitTime;
	}

	public void setWaitTime(Integer waitTime) {
		this.waitTime = waitTime;
	}

	public String getSipName() {
		return sipName;
	}

	public void setSipName(String sipName) {
		this.sipName = sipName;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getEntityId() {
		return entityId;
	}

	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public String getHostName() {
		return hostName;
	}

	public void setHostName(String hostName) {
		this.hostName = hostName;
	}

	public String getPublicAddress() {
		return publicAddress;
	}

	public void setPublicAddress(String publicAddress) {
		this.publicAddress = publicAddress;
	}

	public String getPrivateAddress() {
		return privateAddress;
	}

	public void setPrivateAddress(String privateAddress) {
		this.privateAddress = privateAddress;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public String getPort() {
		return port;
	}

	public void setPort(String port) {
		this.port = port;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getOnlineTime() {
		return onlineTime;
	}

	public void setOnlineTime(Long onlineTime) {
		this.onlineTime = onlineTime;
	}

	public Long getKeepTime() {
		return keepTime;
	}

	public void setKeepTime(Long keepTime) {
		this.keepTime = keepTime;
	}

	public Integer getUse() {
		return use;
	}

	public void setUse(Integer use) {
		this.use = use;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public List<String> getIds() {
		return ids;
	}

	public void setIds(List<String> ids) {
		this.ids = ids;
	}

	public String getAsteriskVersion() {
		return asteriskVersion;
	}

	public void setAsteriskVersion(String asteriskVersion) {
		this.asteriskVersion = asteriskVersion;
	}

	public String getArisVersion() {
		return arisVersion;
	}

	public void setArisVersion(String arisVersion) {
		this.arisVersion = arisVersion;
	}

	public Integer getHbInterval() {
		return hbInterval;
	}

	public void setHbInterval(Integer hbInterval) {
		this.hbInterval = hbInterval;
	}

	public Integer getHbTimeout() {
		return hbTimeout;
	}

	public void setHbTimeout(Integer hbTimeout) {
		this.hbTimeout = hbTimeout;
	}

	public Integer getSyncType() {
		return syncType;
	}

	public void setSyncType(Integer syncType) {
		this.syncType = syncType;
	}

}
