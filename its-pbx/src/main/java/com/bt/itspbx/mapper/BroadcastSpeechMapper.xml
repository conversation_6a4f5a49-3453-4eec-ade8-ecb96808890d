<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itspbx.mapper.BroadcastSpeechMapper">

    <resultMap id="BroadcastSpeechMap" type="com.bt.itspbx.domain.dto.BroadcastSpeechDTO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="request_id" property="requestId"/>
        <result column="task_id" property="taskId"/>
        <result column="send_status" property="sendStatus"/>
        <result column="complete_status" property="completeStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="audio_address" property="audioAddress"/>
    </resultMap>

    <insert id="add" parameterType="com.bt.itspbx.domain.dto.BroadcastSpeechDTO">
        insert into pbx_broadcast_speech (
        id,
        user_id,
        request_id,
        task_id,
        send_status,
        complete_status,
        create_time,
        update_time,
        audio_address
        )
        values (
        #{id},
        #{userId},
        #{requestId},
        #{taskId},
        #{sendStatus},
        #{completeStatus},
        #{createTime},
        #{updateTime},
        #{audioAddress}
        )
    </insert>

    <update id="update" parameterType="com.bt.itspbx.domain.dto.BroadcastSpeechDTO">
        update pbx_broadcast_speech
        set
        update_time=#{updateTime}
        <if test="taskId != null &amp;&amp; taskId != '' ">
            ,task_id=#{taskId}
        </if>
        <if test="sendStatus != null">
            ,send_status=#{sendStatus}
        </if>
        <if test="completeStatus != null">
            ,complete_status=#{completeStatus}
        </if>
        <if test="audioAddress != null &amp;&amp; audioAddress != '' ">
            ,audio_address=#{audioAddress}
        </if>
        where id=#{id}
    </update>

    <select id="selectByRequestId" parameterType="string" resultMap="BroadcastSpeechMap">
        select * from pbx_broadcast_speech where request_id=#{requestId}
    </select>

</mapper>