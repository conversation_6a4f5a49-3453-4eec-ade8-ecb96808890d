package com.bt.itspbx.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.bt.itspbx.domain.dto.SyncLogDTO;
import com.bt.itspbx.domain.vo.SyncLogVO;

@Mapper
public interface SyncLogMapper {
	
	List<SyncLogVO> selectList(SyncLogDTO dto);

	int add(SyncLogDTO dto);

	int update(SyncLogDTO dto);

	int delete(SyncLogDTO dto);

	int batchDelete(SyncLogDTO dto);
	
	String getUserNameByUserId(String userId);
}
