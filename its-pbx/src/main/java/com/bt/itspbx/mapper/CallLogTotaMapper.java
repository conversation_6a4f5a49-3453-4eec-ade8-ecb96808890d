package com.bt.itspbx.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.bt.itspbx.domain.dto.CallLogTotalDTO;
import com.bt.itspbx.domain.vo.CallLogTotalVO;

@Mapper
public interface CallLogTotaMapper {

	List<CallLogTotalVO> selectList(CallLogTotalDTO dto);

	CallLogTotalVO getCallLogTotal(String id);

	CallLogTotalVO getCallLogTotalByCallId(String callId);

	int add(CallLogTotalDTO dto);

	int update(CallLogTotalDTO dto);

	int deleteOther(CallLogTotalDTO dto);

}
