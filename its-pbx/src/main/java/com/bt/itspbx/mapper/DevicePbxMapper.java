package com.bt.itspbx.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.bt.itspbx.domain.dto.DevicePbxDTO;
import com.bt.itspbx.domain.dto.DeviceStatusDTO;
import com.bt.itspbx.domain.dto.ParamsDTO;
import com.bt.itspbx.domain.dto.TreeDTO;
import com.bt.itspbx.domain.vo.DevicePbxVO;
import com.bt.itspbx.domain.vo.DeviceUserIdVO;
import com.bt.itspbx.domain.vo.OrganRoleVO;

/**
 * 
 * @Description: 紧急电话、对讲设备对接
 * <AUTHOR>
 * @date 2023年9月26日 下午5:29:24
 *
 */
@Mapper
public interface DevicePbxMapper {

	/**
	 * 查询隧道紧急电话明细
	 * @param dto
	 * @return
	 */
	DevicePbxVO selectDevicePbx(ParamsDTO dto);

	/**
	 * 获取系统紧急电话、对讲、软电话信息
	 * @param dto
	 * @return
	 */
	List<DevicePbxVO> selectAllDevicePbxList();

	List<DevicePbxVO> selectList(DevicePbxDTO dto);

	List<DevicePbxVO> selectListByIds(DevicePbxDTO dto);

	DevicePbxVO getDevicePbx(String deviceId);

	int add(DevicePbxDTO dto);

	int update(DevicePbxDTO dto);

	int delete(DevicePbxDTO dto);

	int batchDelete(DevicePbxDTO dto);

	int checkDeviceCode(DevicePbxDTO dto);

	List<DevicePbxVO> selectSoftphoneListByUserId(ParamsDTO dto);

	// 紧急电话、亭内对讲查询
	List<DevicePbxVO> getAllEmergencyTel(TreeDTO dto);

	List<DevicePbxVO> getAllSoftphone(TreeDTO dto);

	List<OrganRoleVO> selectOrganRole(TreeDTO dto);

	/**
	 * 批量更新设备主表状态
	 * @param updateList
	 * @return
	 */
	int batchUpdateStatus(List<DeviceStatusDTO> updateList);

	/**
	 * @描述 单个设备状态更新
	 */
	int updateStatus(DeviceStatusDTO dto);

	/**
	 * 获取隧道紧急电话、亭内对讲的操作权限
	 * @return
	 */
	List<DeviceUserIdVO> selectDeviceUserId();

	/**
	 * 软件电话操作权限
	 * @return
	 */
	List<DeviceUserIdVO> selectSoftPhoneUserId();

}
