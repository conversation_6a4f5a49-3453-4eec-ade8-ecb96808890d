<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bt.itspbx.mapper.CallLogTotaMapper">
	<resultMap type="com.bt.itspbx.domain.vo.CallLogTotalVO" id="callLogTotalMap">      
        <result column="call_id" property="callId" ></result>       
        <result column="status" property="status" ></result>
        <result column="src" property="src" ></result>
        <result column="src_name" property="srcName" ></result>
        <result column="src_address" property="srcAddress" ></result>
        <result column="dst" property="dst" ></result>
        <result column="dst_name" property="dstName" ></result>
        <result column="dst_address" property="dstAddress" ></result>
        <result column="start" property="start" ></result>
        <result column="answer" property="answer" ></result>
        <result column="end" property="end" ></result>
        <result column="duration" property="duration" ></result>              
        <result column="create_time" property="createTime" ></result>
        <result column="id" property="id" ></result>
        <result column="type" property="type" ></result>
        <result column="update_time" property="updateTime" ></result>
        <result column="read_flag" property="readFlag" ></result> 
        <result column="answer_flag" property="answerFlag" ></result>  
        <result column="user_id" property="userId" ></result> 
        <result column="answer_code" property="answerCode" ></result>      
    </resultMap>

    <select id="selectList" resultMap="callLogTotalMap" parameterType="com.bt.itspbx.domain.dto.CallLogTotalDTO">
       	SELECT t1.call_id,t1.status,src,src_name,src_address,dst,dst_name,dst_address,t1.`start`,t1.answer,t1.`end`,duration,create_time,
			t2.id,t2.type,t2.user_id,t2.update_time,t2.read_flag,t2.answer_flag,t2.answer_code
		FROM pbx_call_log_total t1,pbx_call_log_user t2
		where t1.call_id=t2.call_id and t1.type in('dial','queue') 
		<if test="src != null &amp;&amp; src != '' "> AND src like CONCAT('%', #{src}, '%') </if>
	    <if test="dst != null &amp;&amp; dst != '' "> AND dst like CONCAT('%', #{dst}, '%') </if>
	    <if test="srcAddress != null &amp;&amp; srcAddress != '' "> AND src_address like CONCAT('%', #{srcAddress}, '%') </if>
	    <if test="dstAddress != null &amp;&amp; dstAddress != '' "> AND dst_address like CONCAT('%', #{dstAddress}, '%') </if>
	    <if test="startTime != null "> AND t1.create_time &gt;=#{startTime}</if>
		<if test="endTime != null "> AND t1.create_time &lt;=#{endTime}</if>
		<if test="readFlag != null "> AND t2.read_flag=#{readFlag} </if>		
	    <choose>
		    <when test="answerStatus == 0">
		        AND t1.answer IS NULL
		    </when>
		    <when test="answerStatus == 1">
		        AND t1.answer IS NOT NULL
		    </when>
		</choose>
	    <if test="userId != null &amp;&amp; userId != '' "> AND t2.user_id=#{userId}</if>
	    <if test="keyword != null &amp;&amp; keyword != '' ">
	      AND CONCAT(IFNULL(src, '' ),IFNULL(dst, '' ),IFNULL(src_address, '' ),IFNULL(dst_address, '' ),IFNULL(src_name, '' ),IFNULL(dst_name, '' ))
	       like CONCAT('%', #{keyword}, '%')
	    </if>
	    order by t1.create_time desc
    </select>

    <select id="getCallLogTotal" resultMap="callLogTotalMap" parameterType="java.lang.String">
       	SELECT id,call_id,status,src,src_name,src_address,dst,dst_name,dst_address,`start`,answer,`end`,duration,create_time
		FROM pbx_call_log_total where id=#{id} LIMIT 1
    </select>
    
    <select id="getCallLogTotalByCallId" resultMap="callLogTotalMap" parameterType="java.lang.String">
       	SELECT id,call_id,status,src,src_name,src_address,dst,dst_name,dst_address,`start`,answer,`end`,duration,create_time
		FROM pbx_call_log_total where call_id=#{callId} LIMIT 1
    </select>

    <insert id="add" parameterType="com.bt.itspbx.domain.dto.CallLogTotalDTO">
		insert into pbx_call_log_total(id,call_id,type,status,src,src_name,src_address,dst,dst_name,dst_address,`start`,answer,`end`,duration,create_time)
		values (#{id},#{callId},#{type},#{status},#{src},#{srcName},#{srcAddress},#{dst},#{dstName},#{dstAddress},#{start},#{answer},#{end},#{duration},#{createTime})		
	</insert>

    <update id="update" parameterType="com.bt.itspbx.domain.dto.CallLogTotalDTO">
		update pbx_call_log_total 
		<set>
		<if test="type != null">type=#{type},</if>
		<if test="callId != null &amp;&amp; callId != '' ">call_id=#{callId},</if>
		<if test="status != null &amp;&amp; status != '' ">status=#{status},</if>
		<if test="src != null &amp;&amp; src != '' ">src=#{src},</if>
		<if test="srcName != null &amp;&amp; srcName != '' ">src_name=#{srcName},</if>
		<if test="srcAddress != null &amp;&amp; srcAddress != '' ">src_address=#{srcAddress},</if>
		<if test="dst != null &amp;&amp; dst != '' ">dst=#{dst},</if>
		<if test="dstName != null &amp;&amp; dstName != '' ">dst_name=#{dstName},</if>
		<if test="dstAddress != null &amp;&amp; dstAddress != '' ">dst_address=#{dstAddress},</if>
		<if test="start != null">`start`=#{start},</if>
		<if test="answer != null">answer=#{answer},</if>
		<if test="end != null">`end`=#{end},</if>
		<if test="duration != null">duration=#{duration},</if>						
		<if test="createTime != null">create_time=#{createTime},</if>
		</set>
		where id=#{id}
	</update>   
	
	<delete id="deleteOther" parameterType="com.bt.itspbx.domain.dto.CallLogTotalDTO">
		delete from pbx_call_log_total where call_id=#{callId} and id!=#{id}
	</delete>

</mapper>