/*
 * package com.bt.itspbx.config;
 * 
 * import java.io.BufferedInputStream; import java.io.FileInputStream; import
 * java.io.IOException; import java.security.KeyStore; import
 * java.security.SecureRandom; import java.time.Duration;
 * 
 * import javax.net.ssl.*;
 * 
 * import org.springframework.beans.factory.annotation.Autowired; import
 * org.springframework.context.annotation.Bean; import
 * org.springframework.context.annotation.Configuration;
 * 
 * import com.bt.itscore.config.BtConfig; import
 * com.bt.itspbx.nats.NatsListener;
 * 
 * import io.nats.client.Connection; import io.nats.client.Nats; import
 * io.nats.client.Options;
 * 
 * @Configuration public class NatsConfiguration2 { private static String
 * PRIVSTORE_PATH = "src/main/resources/certs/privstore.ks"; private static
 * String TRUSTSTORE_PATH = "src/main/resources/certs/truststore.ks"; private
 * static String STORE_PASSWORD = "password"; private static String KEY_PASSWORD
 * = "password"; private static String ALGORITHM = "SunX509"; String tls="";
 * 
 *//**
	* 注入 nats 连接
	* @param properties nats 配置文件类
	* @return  nats 连接
	* @throws Exception 
	*//*
		 * @Bean(name = "natsConnection") public Connection
		 * natsConnection(NatsProperties properties) throws Exception { String[] str =
		 * properties.getNatsUrls().split(","); Options.Builder builder = new
		 * Options.Builder() // 配置 nats 服务器地址 .servers(str) // 配置用户名和密码
		 * .userInfo(properties.getUserName().toCharArray(),
		 * properties.getPassword().toCharArray()) // nats 监听 .connectionListener(new
		 * NatsListener()) // 最大重连次数 .maxReconnects(properties.getMaxReconnect()) //
		 * 重连等待时间 .reconnectWait(Duration.ofSeconds(properties.getReconnectWait())) //
		 * 连接超时时间
		 * .connectionTimeout(Duration.ofSeconds(properties.getConnectionTimeout())); if
		 * (properties.getToken() != null) {
		 * builder.token(properties.getToken().toCharArray()); } // 连接 nats return
		 * Nats.connect(builder.build());
		 * 
		 * }
		 * 
		 * private SSLContext createContext() throws Exception { SSLContext ctx =
		 * SSLContext.getInstance(Options.DEFAULT_SSL_PROTOCOL);
		 * ctx.init(createPrivateKeyManagers(), createTrustManagers(), new
		 * SecureRandom()); return ctx; }
		 * 
		 * private KeyManager[] createPrivateKeyManagers() throws Exception { KeyStore
		 * store = loadKeystore(PRIVSTORE_PATH); KeyManagerFactory factory =
		 * KeyManagerFactory.getInstance(ALGORITHM); factory.init(store,
		 * KEY_PASSWORD.toCharArray()); return factory.getKeyManagers(); }
		 * 
		 * private TrustManager[] createTrustManagers() throws Exception { KeyStore
		 * store = loadKeystore(TRUSTSTORE_PATH); TrustManagerFactory factory =
		 * TrustManagerFactory.getInstance(ALGORITHM); factory.init(store); return
		 * factory.getTrustManagers(); }
		 * 
		 * private KeyStore loadKeystore(String path) throws Exception { KeyStore store
		 * = KeyStore.getInstance("JKS"); BufferedInputStream in = null; try { in = new
		 * BufferedInputStream(new FileInputStream(path)); store.load(in,
		 * KEY_PASSWORD.toCharArray()); } finally { if (in != null) { in.close(); } }
		 * return store; }
		 * 
		 * 
		 * }
		 */